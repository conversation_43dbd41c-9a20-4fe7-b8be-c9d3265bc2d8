package database

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/jackc/pgx/v4"
	"github.com/jackc/pgx/v4/pgxpool"
	"github.com/olzzhas/edunite-server/course_service/pkg/validator"
)

const (
	StatusUnmarked = "unmarked"
	StatusPresent  = "present"
	StatusAbsent   = "absent"
	StatusExcused  = "excused"
)

var (
	ErrAttendanceNotFound = errors.New("attendance not found")
)

type Attendance struct {
	ID             int64     `json:"id"`
	ThreadID       int64     `json:"thread_id"`
	UserID         int64     `json:"user_id"`
	AttendanceDate time.Time `json:"attendance_date"`
	Status         string    `json:"status"` // unmarked, present, absent, excused
	Reason         string    `json:"reason,omitempty"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
}

type AttendanceUser struct {
	ID      int64  `json:"id"`
	Name    string `json:"name"`
	Surname string `json:"surname"`
	Email   string `json:"email"`
	Role    string `json:"role"`
}

type AttendanceWithUser struct {
	ID             int64          `json:"id"`
	ThreadID       int64          `json:"thread_id"`
	UserID         int64          `json:"user_id"`
	AttendanceDate time.Time      `json:"attendance_date"`
	Status         string         `json:"status"`
	Reason         string         `json:"reason"`
	CreatedAt      time.Time      `json:"created_at"`
	UpdatedAt      time.Time      `json:"updated_at"`
	User           AttendanceUser `json:"user"`
}

type AttendanceRepository interface {
	CreateAttendance(ctx context.Context, att *Attendance) error
	BulkCreateAttendance(ctx context.Context, records []*Attendance) error
	GetAttendance(ctx context.Context, id int64) (*Attendance, error)
	ListByThreadAndDate(ctx context.Context, threadID int64, date time.Time) ([]*Attendance, error)
	ListByThreadAndDateWithUsers(ctx context.Context, threadID int64, date time.Time) ([]*AttendanceWithUser, error)
	ListByThread(ctx context.Context, threadID int64) ([]*Attendance, error)
	ListByUser(ctx context.Context, userID int64) ([]*Attendance, error)
	UpdateAttendance(ctx context.Context, att *Attendance) error
	DeleteAttendance(ctx context.Context, id int64) error
	DeleteByThreadAndUser(ctx context.Context, threadID, userID int64) error
}

type attendanceRepository struct {
	db *pgxpool.Pool
}

func NewAttendanceRepository(db *pgxpool.Pool) AttendanceRepository {
	return &attendanceRepository{db: db}
}

func ValidateAttendance(v *validator.Validator, a *Attendance, allowIDZero bool) {
	if !allowIDZero {
		v.Check(a.ID > 0, "id", "must be > 0")
	}

	v.Check(a.ThreadID > 0, "thread_id", "must be > 0")
	v.Check(a.UserID > 0, "user_id", "must be > 0")
	v.Check(!a.AttendanceDate.IsZero(), "attendance_date", "must be provided")
	v.Check(validator.PermittedValue(a.Status,
		StatusUnmarked, StatusPresent, StatusAbsent, StatusExcused),
		"status", "invalid value")
	v.Check(len(a.Reason) <= 400, "reason", "max 400 characters")
}

// CreateAttendance добавляет одну запись
func (r *attendanceRepository) CreateAttendance(ctx context.Context, att *Attendance) error {
	const q = `
	INSERT INTO attendance (thread_id, user_id, attendance_date, status, reason)
	VALUES ($1,$2,$3,$4,$5)
	RETURNING id, created_at, updated_at
	`
	return r.db.QueryRow(ctx, q,
		att.ThreadID, att.UserID, att.AttendanceDate, att.Status, att.Reason).
		Scan(&att.ID, &att.CreatedAt, &att.UpdatedAt)
}

// BulkCreateAttendance добавляет несколько записей за раз
func (r *attendanceRepository) BulkCreateAttendance(ctx context.Context, records []*Attendance) error {
	if len(records) == 0 {
		return nil
	}

	// Построим VALUES ($1,$2,$3,$4,$5), … с пошаговой индексацией
	valStrings := make([]string, 0, len(records))
	valArgs := make([]interface{}, 0, len(records)*5)

	for i, a := range records {
		idx := i*5 + 1
		valStrings = append(valStrings,
			fmt.Sprintf("($%d,$%d,$%d,$%d,$%d)", idx, idx+1, idx+2, idx+3, idx+4))

		valArgs = append(valArgs,
			a.ThreadID,
			a.UserID,
			a.AttendanceDate,
			a.Status,
			a.Reason,
		)
	}

	q := fmt.Sprintf(`
		INSERT INTO attendance (thread_id, user_id, attendance_date, status, reason)
		VALUES %s
		ON CONFLICT (thread_id, user_id, attendance_date) DO NOTHING`, strings.Join(valStrings, ","))

	_, err := r.db.Exec(ctx, q, valArgs...)
	return err
}

// GetAttendance получение записи по ID
func (r *attendanceRepository) GetAttendance(ctx context.Context, id int64) (*Attendance, error) {
	const q = `
	SELECT id, thread_id, user_id, attendance_date, status, reason,
	       created_at, updated_at
	  FROM attendance
	 WHERE id=$1`
	var a Attendance
	err := r.db.QueryRow(ctx, q, id).Scan(
		&a.ID, &a.ThreadID, &a.UserID, &a.AttendanceDate,
		&a.Status, &a.Reason, &a.CreatedAt, &a.UpdatedAt)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, ErrAttendanceNotFound
		}
		return nil, err
	}
	return &a, nil
}

// ListByThreadAndDate возвращает посещаемость на конкретную дату
func (r *attendanceRepository) ListByThreadAndDate(ctx context.Context, threadID int64, date time.Time) ([]*Attendance, error) {
	const q = `
	SELECT id, thread_id, user_id, attendance_date, status, reason,
	       created_at, updated_at
	  FROM attendance
	 WHERE thread_id=$1 AND attendance_date=$2
	 ORDER BY user_id`
	return r.list(ctx, q, threadID, date)
}

// ListByThreadAndDateWithUsers возвращает посещаемость на конкретную дату с информацией о пользователях
func (r *attendanceRepository) ListByThreadAndDateWithUsers(ctx context.Context, threadID int64, date time.Time) ([]*AttendanceWithUser, error) {
	const q = `
	SELECT a.id, a.thread_id, a.user_id, a.attendance_date, a.status, a.reason,
	       a.created_at, a.updated_at,
	       u.id, u.name, u.surname, u.email, u.role
	  FROM attendance a
	  JOIN users u ON a.user_id = u.id
	 WHERE a.thread_id=$1 AND a.attendance_date=$2
	 ORDER BY u.surname, u.name`

	rows, err := r.db.Query(ctx, q, threadID, date)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var out []*AttendanceWithUser
	for rows.Next() {
		var a AttendanceWithUser
		if err := rows.Scan(
			&a.ID, &a.ThreadID, &a.UserID, &a.AttendanceDate,
			&a.Status, &a.Reason, &a.CreatedAt, &a.UpdatedAt,
			&a.User.ID, &a.User.Name, &a.User.Surname, &a.User.Email, &a.User.Role); err != nil {
			return nil, err
		}
		out = append(out, &a)
	}
	return out, rows.Err()
}

// ListByThread возвращает все записи по потоку
func (r *attendanceRepository) ListByThread(ctx context.Context, threadID int64) ([]*Attendance, error) {
	const q = `
	SELECT id, thread_id, user_id, attendance_date, status, reason,
	       created_at, updated_at
	  FROM attendance
	 WHERE thread_id=$1
	 ORDER BY attendance_date, user_id`
	return r.list(ctx, q, threadID)
}

// ListByUser возвращает все записи по пользователю
func (r *attendanceRepository) ListByUser(ctx context.Context, userID int64) ([]*Attendance, error) {
	const q = `
	SELECT id, thread_id, user_id, attendance_date, status, reason,
	       created_at, updated_at
	  FROM attendance
	 WHERE user_id=$1
	 ORDER BY attendance_date`
	return r.list(ctx, q, userID)
}

// UpdateAttendance обновляет статус и причину посещения
func (r *attendanceRepository) UpdateAttendance(ctx context.Context, att *Attendance) error {
	const q = `
	UPDATE attendance
	   SET status=$1,
	       reason=$2,
	       updated_at=NOW()
	 WHERE id=$3
	RETURNING updated_at`
	if err := r.db.QueryRow(ctx, q, att.Status, att.Reason, att.ID).
		Scan(&att.UpdatedAt); err != nil {

		if errors.Is(err, pgx.ErrNoRows) {
			return ErrAttendanceNotFound
		}
		return err
	}
	return nil
}

// DeleteAttendance удаляет запись
func (r *attendanceRepository) DeleteAttendance(ctx context.Context, id int64) error {
	cmd, err := r.db.Exec(ctx, `DELETE FROM attendance WHERE id=$1`, id)
	if err != nil {
		return err
	}
	if cmd.RowsAffected() == 0 {
		return ErrAttendanceNotFound
	}
	return nil
}

// DeleteByThreadAndUser удаляет все записи посещаемости для заданного потока и пользователя.
func (r *attendanceRepository) DeleteByThreadAndUser(ctx context.Context, threadID, userID int64) error {
	_, err := r.db.Exec(ctx,
		`DELETE FROM attendance WHERE thread_id=$1 AND user_id=$2`, threadID, userID)
	return err
}

func (r *attendanceRepository) list(ctx context.Context, query string, args ...interface{}) ([]*Attendance, error) {
	rows, err := r.db.Query(ctx, query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var out []*Attendance
	for rows.Next() {
		var a Attendance
		if err := rows.Scan(
			&a.ID, &a.ThreadID, &a.UserID, &a.AttendanceDate,
			&a.Status, &a.Reason, &a.CreatedAt, &a.UpdatedAt); err != nil {
			return nil, err
		}
		out = append(out, &a)
	}
	return out, rows.Err()
}
