package models

import (
	"time"
)

type NotificationType string

const (
	NotificationTypeInfo         NotificationType = "info"
	NotificationTypeWarning      NotificationType = "warning"
	NotificationTypeSuccess      NotificationType = "success"
	NotificationTypeError        NotificationType = "error"
	NotificationTypeAnnouncement NotificationType = "announcement"
)

type NotificationPriority string

const (
	NotificationPriorityLow    NotificationPriority = "low"
	NotificationPriorityNormal NotificationPriority = "normal"
	NotificationPriorityHigh   NotificationPriority = "high"
	NotificationPriorityUrgent NotificationPriority = "urgent"
)

type TargetType string

const (
	TargetTypeAll    TargetType = "all"
	TargetTypeRole   TargetType = "role"
	TargetTypeUser   TargetType = "user"
	TargetTypeDegree TargetType = "degree"
	TargetTypeCourse TargetType = "course"
	TargetTypeThread TargetType = "thread"
)

type Notification struct {
	ID            int64                 `json:"id" db:"id"`
	Title         string                `json:"title" db:"title"`
	Message       string                `json:"message" db:"message"`
	Type          NotificationType      `json:"type" db:"type"`
	Priority      NotificationPriority  `json:"priority" db:"priority"`
	TargetType    TargetType            `json:"target_type" db:"target_type"`
	TargetValue   *string               `json:"target_value" db:"target_value"`
	SenderID      *int64                `json:"sender_id" db:"sender_id"`
	SendEmail     bool                  `json:"send_email" db:"send_email"`
	EmailSubject  *string               `json:"email_subject" db:"email_subject"`
	EmailTemplate *string               `json:"email_template" db:"email_template"`
	ScheduledAt   *time.Time            `json:"scheduled_at" db:"scheduled_at"`
	SentAt        *time.Time            `json:"sent_at" db:"sent_at"`
	CreatedAt     time.Time             `json:"created_at" db:"created_at"`
	UpdatedAt     time.Time             `json:"updated_at" db:"updated_at"`
}

type NotificationRecipient struct {
	ID             int64      `json:"id" db:"id"`
	NotificationID int64      `json:"notification_id" db:"notification_id"`
	UserID         int64      `json:"user_id" db:"user_id"`
	IsRead         bool       `json:"is_read" db:"is_read"`
	ReadAt         *time.Time `json:"read_at" db:"read_at"`
	EmailSent      bool       `json:"email_sent" db:"email_sent"`
	EmailSentAt    *time.Time `json:"email_sent_at" db:"email_sent_at"`
	CreatedAt      time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt      time.Time  `json:"updated_at" db:"updated_at"`
}

type UserNotification struct {
	Notification *Notification          `json:"notification"`
	Recipient    *NotificationRecipient `json:"recipient"`
}

type EmailTemplate struct {
	ID           int64     `json:"id" db:"id"`
	Name         string    `json:"name" db:"name"`
	Subject      string    `json:"subject" db:"subject"`
	HTMLContent  string    `json:"html_content" db:"html_content"`
	TextContent  *string   `json:"text_content" db:"text_content"`
	Variables    *string   `json:"variables" db:"variables"` // JSON string
	IsActive     bool      `json:"is_active" db:"is_active"`
	CreatedAt    time.Time `json:"created_at" db:"created_at"`
	UpdatedAt    time.Time `json:"updated_at" db:"updated_at"`
}

type User struct {
	ID       int64  `json:"id" db:"id"`
	Name     string `json:"name" db:"name"`
	Surname  string `json:"surname" db:"surname"`
	Email    string `json:"email" db:"email"`
	Role     string `json:"role" db:"role"`
	DegreeID *int64 `json:"degree_id" db:"degree_id"`
}

type NotificationStats struct {
	TotalNotifications  int32 `json:"total_notifications"`
	UnreadNotifications int32 `json:"unread_notifications"`
	ReadNotifications   int32 `json:"read_notifications"`
	EmailNotifications  int32 `json:"email_notifications"`
}

type CreateNotificationRequest struct {
	Title         string                `json:"title"`
	Message       string                `json:"message"`
	Type          NotificationType      `json:"type"`
	Priority      NotificationPriority  `json:"priority"`
	TargetType    TargetType            `json:"target_type"`
	TargetValue   *string               `json:"target_value"`
	SenderID      *int64                `json:"sender_id"`
	SendEmail     bool                  `json:"send_email"`
	EmailSubject  *string               `json:"email_subject"`
	EmailTemplate *string               `json:"email_template"`
	ScheduledAt   *time.Time            `json:"scheduled_at"`
}
