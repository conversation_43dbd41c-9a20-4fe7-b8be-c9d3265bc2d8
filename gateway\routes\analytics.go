package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/olzzhas/edunite-server/gateway/clients"
	"github.com/olzzhas/edunite-server/gateway/handlers"
)

// SetupAnalyticsRoutes sets up analytics routes
func SetupAnalyticsRoutes(r *gin.Engine, authClient *clients.AuthClient, analyticsHandler *handlers.AnalyticsHandler) {
	// Analytics API group
	analyticsGroup := r.Group("/api/analytics")
	
	// Apply authentication middleware for all analytics routes
	// analyticsGroup.Use(AuthMiddleware(authClient))
	
	{
		// Dashboard Overview
		analyticsGroup.GET("/overview", analyticsHandler.GetOverview)
		
		// Users Timeline
		analyticsGroup.GET("/users-timeline", analyticsHandler.GetUsersTimeline)
		
		// User Demographics
		analyticsGroup.GET("/user-demographics", analyticsHandler.GetUserDemographics)
		
		// Recent Activities
		analyticsGroup.GET("/recent-activities", analyticsHandler.GetRecentActivities)
		
		// Detailed Stats
		analyticsGroup.GET("/detailed-stats", analyticsHandler.GetDetailedStats)
		
		// Performance Metrics
		analyticsGroup.GET("/performance-metrics", analyticsHandler.GetPerformanceMetrics)
	}

	// Additional analytics endpoints as specified in the API spec
	
	// Courses analytics
	coursesGroup := analyticsGroup.Group("/courses")
	{
		// TODO: Implement these endpoints
		// coursesGroup.GET("/popular", analyticsHandler.GetPopularCourses)
		// coursesGroup.GET("/completion-rates", analyticsHandler.GetCourseCompletionRates)
	}

	// Students analytics
	studentsGroup := analyticsGroup.Group("/students")
	{
		// TODO: Implement these endpoints
		// studentsGroup.GET("/engagement", analyticsHandler.GetStudentEngagement)
		// studentsGroup.GET("/performance", analyticsHandler.GetStudentPerformance)
	}

	// Teachers analytics
	teachersGroup := analyticsGroup.Group("/teachers")
	{
		// TODO: Implement these endpoints
		// teachersGroup.GET("/activity", analyticsHandler.GetTeacherActivity)
		// teachersGroup.GET("/course-load", analyticsHandler.GetTeacherCourseLoad)
	}

	// Attendance analytics
	attendanceGroup := analyticsGroup.Group("/attendance")
	{
		// TODO: Implement these endpoints
		// attendanceGroup.GET("/trends", analyticsHandler.GetAttendanceTrends)
		// attendanceGroup.GET("/by-course", analyticsHandler.GetAttendanceByCourse)
	}
}
