.PHONY: proto build run clean

# Generate protobuf files
proto:
	@echo "Generating protobuf files..."
	@mkdir -p pb/analytics
	protoc --go_out=pb/analytics --go_opt=paths=source_relative \
		--go-grpc_out=pb/analytics --go-grpc_opt=paths=source_relative \
		proto/analytics.proto

# Build the service
build: proto
	@echo "Building analytics service..."
	go build -o bin/analytics_service cmd/main.go

# Run the service
run: build
	@echo "Running analytics service..."
	./bin/analytics_service

# Clean generated files
clean:
	@echo "Cleaning generated files..."
	rm -rf pb/
	rm -rf bin/

# Install dependencies
deps:
	go mod tidy
	go mod download

# Test
test:
	go test ./...

# Development run (with auto-reload)
dev:
	go run cmd/main.go
