### Test Student Profile Endpoint

# Получить профиль студента с ID 1
GET http://localhost:8081/students/1/profile
Content-Type: application/json

###

# Получить профиль студента с ID 2
GET http://localhost:8081/students/2/profile
Content-Type: application/json

###

# Получить профиль студента с ID 3
GET http://localhost:8081/students/3/profile
Content-Type: application/json

###

# Тест с неверным ID (должен вернуть 400)
GET http://localhost:8081/students/invalid/profile
Content-Type: application/json

###

# Тест с несуществующим ID (может вернуть 500 или пустые данные)
GET http://localhost:8081/students/999999/profile
Content-Type: application/json
