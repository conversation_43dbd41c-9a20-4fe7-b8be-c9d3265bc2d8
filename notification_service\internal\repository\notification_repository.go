package repository

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/jackc/pgx/v4"
	"github.com/jackc/pgx/v4/pgxpool"
	"github.com/olzzhas/edunite-server/notification_service/internal/models"
)

type NotificationRepository interface {
	CreateNotification(ctx context.Context, notification *models.Notification) (*models.Notification, error)
	GetNotificationByID(ctx context.Context, id int64) (*models.Notification, error)
	GetAllNotifications(ctx context.Context, page, limit int32, targetType models.TargetType, targetValue string) ([]*models.Notification, int32, error)
	UpdateNotification(ctx context.Context, notification *models.Notification) error
	DeleteNotification(ctx context.Context, id int64) error
	GetScheduledNotifications(ctx context.Context) ([]*models.Notification, error)
	MarkAsSent(ctx context.Context, id int64) error

	// Recipients
	CreateRecipients(ctx context.Context, notificationID int64, userIDs []int64) error
	GetUserNotifications(ctx context.Context, userID int64, page, limit int32, unreadOnly bool) ([]*models.UserNotification, int32, int32, error)
	MarkAsRead(ctx context.Context, notificationID, userID int64) error
	GetNotificationStats(ctx context.Context, userID int64) (*models.NotificationStats, error)

	// Users
	GetUsersByRole(ctx context.Context, role string) ([]*models.User, error)
	GetUsersByDegree(ctx context.Context, degreeID int64) ([]*models.User, error)
	GetUsersByCourse(ctx context.Context, courseID int64) ([]*models.User, error)
	GetUsersByThread(ctx context.Context, threadID int64) ([]*models.User, error)
	GetAllUsers(ctx context.Context) ([]*models.User, error)
	GetUserByID(ctx context.Context, userID int64) (*models.User, error)

	// Email templates
	GetEmailTemplate(ctx context.Context, name string) (*models.EmailTemplate, error)
	GetEmailTemplates(ctx context.Context, activeOnly bool) ([]*models.EmailTemplate, int32, error)
}

type notificationRepository struct {
	db *pgxpool.Pool
}

func NewNotificationRepository(db *pgxpool.Pool) NotificationRepository {
	return &notificationRepository{db: db}
}

func (r *notificationRepository) CreateNotification(ctx context.Context, notification *models.Notification) (*models.Notification, error) {
	query := `
		INSERT INTO notifications (title, message, type, priority, target_type, target_value, sender_id, 
			send_email, email_subject, email_template, scheduled_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
		RETURNING id, created_at, updated_at`

	var id int64
	var createdAt, updatedAt time.Time

	err := r.db.QueryRow(ctx, query,
		notification.Title,
		notification.Message,
		notification.Type,
		notification.Priority,
		notification.TargetType,
		notification.TargetValue,
		notification.SenderID,
		notification.SendEmail,
		notification.EmailSubject,
		notification.EmailTemplate,
		notification.ScheduledAt,
	).Scan(&id, &createdAt, &updatedAt)

	if err != nil {
		return nil, fmt.Errorf("failed to create notification: %w", err)
	}

	notification.ID = id
	notification.CreatedAt = createdAt
	notification.UpdatedAt = updatedAt

	return notification, nil
}

func (r *notificationRepository) GetNotificationByID(ctx context.Context, id int64) (*models.Notification, error) {
	query := `
		SELECT id, title, message, type, priority, target_type, target_value, sender_id,
			send_email, email_subject, email_template, scheduled_at, sent_at, created_at, updated_at
		FROM notifications WHERE id = $1`

	notification := &models.Notification{}
	err := r.db.QueryRow(ctx, query, id).Scan(
		&notification.ID,
		&notification.Title,
		&notification.Message,
		&notification.Type,
		&notification.Priority,
		&notification.TargetType,
		&notification.TargetValue,
		&notification.SenderID,
		&notification.SendEmail,
		&notification.EmailSubject,
		&notification.EmailTemplate,
		&notification.ScheduledAt,
		&notification.SentAt,
		&notification.CreatedAt,
		&notification.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, fmt.Errorf("notification not found")
		}
		return nil, fmt.Errorf("failed to get notification: %w", err)
	}

	return notification, nil
}

func (r *notificationRepository) GetAllNotifications(ctx context.Context, page, limit int32, targetType models.TargetType, targetValue string) ([]*models.Notification, int32, error) {
	offset := (page - 1) * limit

	whereClause := ""
	args := []interface{}{}
	argIndex := 1

	if targetType != "" {
		whereClause += fmt.Sprintf(" WHERE target_type = $%d", argIndex)
		args = append(args, targetType)
		argIndex++

		if targetValue != "" {
			whereClause += fmt.Sprintf(" AND target_value = $%d", argIndex)
			args = append(args, targetValue)
			argIndex++
		}
	}

	// Count query
	countQuery := "SELECT COUNT(*) FROM notifications" + whereClause
	var totalCount int32
	err := r.db.QueryRow(ctx, countQuery, args...).Scan(&totalCount)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count notifications: %w", err)
	}

	// Data query
	query := fmt.Sprintf(`
		SELECT id, title, message, type, priority, target_type, target_value, sender_id,
			send_email, email_subject, email_template, scheduled_at, sent_at, created_at, updated_at
		FROM notifications%s
		ORDER BY created_at DESC
		LIMIT $%d OFFSET $%d`, whereClause, argIndex, argIndex+1)

	args = append(args, limit, offset)

	rows, err := r.db.Query(ctx, query, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get notifications: %w", err)
	}
	defer rows.Close()

	var notifications []*models.Notification
	for rows.Next() {
		notification := &models.Notification{}
		err := rows.Scan(
			&notification.ID,
			&notification.Title,
			&notification.Message,
			&notification.Type,
			&notification.Priority,
			&notification.TargetType,
			&notification.TargetValue,
			&notification.SenderID,
			&notification.SendEmail,
			&notification.EmailSubject,
			&notification.EmailTemplate,
			&notification.ScheduledAt,
			&notification.SentAt,
			&notification.CreatedAt,
			&notification.UpdatedAt,
		)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to scan notification: %w", err)
		}
		notifications = append(notifications, notification)
	}

	return notifications, totalCount, nil
}

func (r *notificationRepository) UpdateNotification(ctx context.Context, notification *models.Notification) error {
	query := `
		UPDATE notifications
		SET title = $2, message = $3, type = $4, priority = $5, target_type = $6,
			target_value = $7, send_email = $8, email_subject = $9, email_template = $10,
			scheduled_at = $11, updated_at = CURRENT_TIMESTAMP
		WHERE id = $1`

	_, err := r.db.Exec(ctx, query,
		notification.ID,
		notification.Title,
		notification.Message,
		notification.Type,
		notification.Priority,
		notification.TargetType,
		notification.TargetValue,
		notification.SendEmail,
		notification.EmailSubject,
		notification.EmailTemplate,
		notification.ScheduledAt,
	)

	if err != nil {
		return fmt.Errorf("failed to update notification: %w", err)
	}

	return nil
}

func (r *notificationRepository) DeleteNotification(ctx context.Context, id int64) error {
	query := `DELETE FROM notifications WHERE id = $1`

	result, err := r.db.Exec(ctx, query, id)
	if err != nil {
		return fmt.Errorf("failed to delete notification: %w", err)
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("notification not found")
	}

	return nil
}

func (r *notificationRepository) GetScheduledNotifications(ctx context.Context) ([]*models.Notification, error) {
	query := `
		SELECT id, title, message, type, priority, target_type, target_value, sender_id,
			send_email, email_subject, email_template, scheduled_at, sent_at, created_at, updated_at
		FROM notifications
		WHERE scheduled_at IS NOT NULL
			AND scheduled_at <= CURRENT_TIMESTAMP
			AND sent_at IS NULL
		ORDER BY scheduled_at ASC`

	rows, err := r.db.Query(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to get scheduled notifications: %w", err)
	}
	defer rows.Close()

	var notifications []*models.Notification
	for rows.Next() {
		notification := &models.Notification{}
		err := rows.Scan(
			&notification.ID,
			&notification.Title,
			&notification.Message,
			&notification.Type,
			&notification.Priority,
			&notification.TargetType,
			&notification.TargetValue,
			&notification.SenderID,
			&notification.SendEmail,
			&notification.EmailSubject,
			&notification.EmailTemplate,
			&notification.ScheduledAt,
			&notification.SentAt,
			&notification.CreatedAt,
			&notification.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan notification: %w", err)
		}
		notifications = append(notifications, notification)
	}

	return notifications, nil
}

func (r *notificationRepository) MarkAsSent(ctx context.Context, id int64) error {
	query := `UPDATE notifications SET sent_at = CURRENT_TIMESTAMP WHERE id = $1`

	_, err := r.db.Exec(ctx, query, id)
	if err != nil {
		return fmt.Errorf("failed to mark notification as sent: %w", err)
	}

	return nil
}

// Recipients methods
func (r *notificationRepository) CreateRecipients(ctx context.Context, notificationID int64, userIDs []int64) error {
	if len(userIDs) == 0 {
		return nil
	}

	// Build bulk insert query
	valueStrings := make([]string, 0, len(userIDs))
	valueArgs := make([]interface{}, 0, len(userIDs)*2)

	for i, userID := range userIDs {
		valueStrings = append(valueStrings, fmt.Sprintf("($%d, $%d)", i*2+1, i*2+2))
		valueArgs = append(valueArgs, notificationID, userID)
	}

	query := fmt.Sprintf(`
		INSERT INTO notification_recipients (notification_id, user_id)
		VALUES %s
		ON CONFLICT (notification_id, user_id) DO NOTHING`,
		strings.Join(valueStrings, ","))

	_, err := r.db.Exec(ctx, query, valueArgs...)
	if err != nil {
		return fmt.Errorf("failed to create recipients: %w", err)
	}

	return nil
}

func (r *notificationRepository) GetUserNotifications(ctx context.Context, userID int64, page, limit int32, unreadOnly bool) ([]*models.UserNotification, int32, int32, error) {
	offset := (page - 1) * limit

	whereClause := "WHERE nr.user_id = $1"
	args := []interface{}{userID}
	argIndex := 2

	if unreadOnly {
		whereClause += fmt.Sprintf(" AND nr.is_read = $%d", argIndex)
		args = append(args, false)
		argIndex++
	}

	// Count total notifications
	countQuery := fmt.Sprintf(`
		SELECT COUNT(*)
		FROM notification_recipients nr
		JOIN notifications n ON nr.notification_id = n.id
		%s`, whereClause)

	var totalCount int32
	err := r.db.QueryRow(ctx, countQuery, args...).Scan(&totalCount)
	if err != nil {
		return nil, 0, 0, fmt.Errorf("failed to count user notifications: %w", err)
	}

	// Count unread notifications
	unreadCountQuery := `
		SELECT COUNT(*)
		FROM notification_recipients nr
		WHERE nr.user_id = $1 AND nr.is_read = false`

	var unreadCount int32
	err = r.db.QueryRow(ctx, unreadCountQuery, userID).Scan(&unreadCount)
	if err != nil {
		return nil, 0, 0, fmt.Errorf("failed to count unread notifications: %w", err)
	}

	// Get notifications data
	dataQuery := fmt.Sprintf(`
		SELECT
			n.id, n.title, n.message, n.type, n.priority, n.target_type, n.target_value,
			n.sender_id, n.send_email, n.email_subject, n.email_template, n.scheduled_at,
			n.sent_at, n.created_at, n.updated_at,
			nr.id, nr.notification_id, nr.user_id, nr.is_read, nr.read_at,
			nr.email_sent, nr.email_sent_at, nr.created_at, nr.updated_at
		FROM notification_recipients nr
		JOIN notifications n ON nr.notification_id = n.id
		%s
		ORDER BY n.created_at DESC
		LIMIT $%d OFFSET $%d`, whereClause, argIndex, argIndex+1)

	args = append(args, limit, offset)

	rows, err := r.db.Query(ctx, dataQuery, args...)
	if err != nil {
		return nil, 0, 0, fmt.Errorf("failed to get user notifications: %w", err)
	}
	defer rows.Close()

	var userNotifications []*models.UserNotification
	for rows.Next() {
		notification := &models.Notification{}
		recipient := &models.NotificationRecipient{}

		err := rows.Scan(
			&notification.ID, &notification.Title, &notification.Message, &notification.Type,
			&notification.Priority, &notification.TargetType, &notification.TargetValue,
			&notification.SenderID, &notification.SendEmail, &notification.EmailSubject,
			&notification.EmailTemplate, &notification.ScheduledAt, &notification.SentAt,
			&notification.CreatedAt, &notification.UpdatedAt,
			&recipient.ID, &recipient.NotificationID, &recipient.UserID, &recipient.IsRead,
			&recipient.ReadAt, &recipient.EmailSent, &recipient.EmailSentAt,
			&recipient.CreatedAt, &recipient.UpdatedAt,
		)
		if err != nil {
			return nil, 0, 0, fmt.Errorf("failed to scan user notification: %w", err)
		}

		userNotifications = append(userNotifications, &models.UserNotification{
			Notification: notification,
			Recipient:    recipient,
		})
	}

	return userNotifications, totalCount, unreadCount, nil
}

func (r *notificationRepository) MarkAsRead(ctx context.Context, notificationID, userID int64) error {
	query := `
		UPDATE notification_recipients
		SET is_read = true, read_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
		WHERE notification_id = $1 AND user_id = $2`

	result, err := r.db.Exec(ctx, query, notificationID, userID)
	if err != nil {
		return fmt.Errorf("failed to mark notification as read: %w", err)
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("notification recipient not found")
	}

	return nil
}

func (r *notificationRepository) GetNotificationStats(ctx context.Context, userID int64) (*models.NotificationStats, error) {
	query := `
		SELECT
			COUNT(*) as total,
			COUNT(CASE WHEN nr.is_read = false THEN 1 END) as unread,
			COUNT(CASE WHEN nr.is_read = true THEN 1 END) as read,
			COUNT(CASE WHEN nr.email_sent = true THEN 1 END) as email_sent
		FROM notification_recipients nr
		WHERE nr.user_id = $1`

	stats := &models.NotificationStats{}
	err := r.db.QueryRow(ctx, query, userID).Scan(
		&stats.TotalNotifications,
		&stats.UnreadNotifications,
		&stats.ReadNotifications,
		&stats.EmailNotifications,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to get notification stats: %w", err)
	}

	return stats, nil
}

// User methods
func (r *notificationRepository) GetUsersByRole(ctx context.Context, role string) ([]*models.User, error) {
	query := `
		SELECT id, name, surname, email, role, degree_id
		FROM users
		WHERE role = $1`

	rows, err := r.db.Query(ctx, query, role)
	if err != nil {
		return nil, fmt.Errorf("failed to get users by role: %w", err)
	}
	defer rows.Close()

	var users []*models.User
	for rows.Next() {
		user := &models.User{}
		err := rows.Scan(&user.ID, &user.Name, &user.Surname, &user.Email, &user.Role, &user.DegreeID)
		if err != nil {
			return nil, fmt.Errorf("failed to scan user: %w", err)
		}
		users = append(users, user)
	}

	return users, nil
}

func (r *notificationRepository) GetUsersByDegree(ctx context.Context, degreeID int64) ([]*models.User, error) {
	query := `
		SELECT id, name, surname, email, role, degree_id
		FROM users
		WHERE degree_id = $1`

	rows, err := r.db.Query(ctx, query, degreeID)
	if err != nil {
		return nil, fmt.Errorf("failed to get users by degree: %w", err)
	}
	defer rows.Close()

	var users []*models.User
	for rows.Next() {
		user := &models.User{}
		err := rows.Scan(&user.ID, &user.Name, &user.Surname, &user.Email, &user.Role, &user.DegreeID)
		if err != nil {
			return nil, fmt.Errorf("failed to scan user: %w", err)
		}
		users = append(users, user)
	}

	return users, nil
}

func (r *notificationRepository) GetUsersByCourse(ctx context.Context, courseID int64) ([]*models.User, error) {
	query := `
		SELECT DISTINCT u.id, u.name, u.surname, u.email, u.role, u.degree_id
		FROM users u
		JOIN thread_registrations tr ON u.id = tr.user_id
		JOIN threads t ON tr.thread_id = t.id
		WHERE t.course_id = $1`

	rows, err := r.db.Query(ctx, query, courseID)
	if err != nil {
		return nil, fmt.Errorf("failed to get users by course: %w", err)
	}
	defer rows.Close()

	var users []*models.User
	for rows.Next() {
		user := &models.User{}
		err := rows.Scan(&user.ID, &user.Name, &user.Surname, &user.Email, &user.Role, &user.DegreeID)
		if err != nil {
			return nil, fmt.Errorf("failed to scan user: %w", err)
		}
		users = append(users, user)
	}

	return users, nil
}

func (r *notificationRepository) GetUsersByThread(ctx context.Context, threadID int64) ([]*models.User, error) {
	query := `
		SELECT u.id, u.name, u.surname, u.email, u.role, u.degree_id
		FROM users u
		JOIN thread_registrations tr ON u.id = tr.user_id
		WHERE tr.thread_id = $1`

	rows, err := r.db.Query(ctx, query, threadID)
	if err != nil {
		return nil, fmt.Errorf("failed to get users by thread: %w", err)
	}
	defer rows.Close()

	var users []*models.User
	for rows.Next() {
		user := &models.User{}
		err := rows.Scan(&user.ID, &user.Name, &user.Surname, &user.Email, &user.Role, &user.DegreeID)
		if err != nil {
			return nil, fmt.Errorf("failed to scan user: %w", err)
		}
		users = append(users, user)
	}

	return users, nil
}

func (r *notificationRepository) GetAllUsers(ctx context.Context) ([]*models.User, error) {
	query := `
		SELECT id, name, surname, email, role, degree_id
		FROM users`

	rows, err := r.db.Query(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to get all users: %w", err)
	}
	defer rows.Close()

	var users []*models.User
	for rows.Next() {
		user := &models.User{}
		err := rows.Scan(&user.ID, &user.Name, &user.Surname, &user.Email, &user.Role, &user.DegreeID)
		if err != nil {
			return nil, fmt.Errorf("failed to scan user: %w", err)
		}
		users = append(users, user)
	}

	return users, nil
}

func (r *notificationRepository) GetUserByID(ctx context.Context, userID int64) (*models.User, error) {
	query := `
		SELECT id, name, surname, email, role, degree_id
		FROM users
		WHERE id = $1`

	user := &models.User{}
	err := r.db.QueryRow(ctx, query, userID).Scan(
		&user.ID, &user.Name, &user.Surname, &user.Email, &user.Role, &user.DegreeID)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	return user, nil
}

func (r *notificationRepository) GetEmailTemplate(ctx context.Context, name string) (*models.EmailTemplate, error) {
	query := `
		SELECT id, name, subject, html_content, text_content, variables, is_active, created_at, updated_at
		FROM email_templates
		WHERE name = $1 AND is_active = true`

	template := &models.EmailTemplate{}
	err := r.db.QueryRow(ctx, query, name).Scan(
		&template.ID, &template.Name, &template.Subject, &template.HTMLContent,
		&template.TextContent, &template.Variables, &template.IsActive,
		&template.CreatedAt, &template.UpdatedAt)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("email template not found")
		}
		return nil, fmt.Errorf("failed to get email template: %w", err)
	}

	return template, nil
}

func (r *notificationRepository) GetEmailTemplates(ctx context.Context, activeOnly bool) ([]*models.EmailTemplate, int32, error) {
	whereClause := ""
	args := []interface{}{}

	if activeOnly {
		whereClause = " WHERE is_active = true"
	}

	// Count query
	countQuery := "SELECT COUNT(*) FROM email_templates" + whereClause
	var totalCount int32
	err := r.db.QueryRow(ctx, countQuery, args...).Scan(&totalCount)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count email templates: %w", err)
	}

	// Data query
	query := `
		SELECT id, name, subject, html_content, text_content, variables, is_active, created_at, updated_at
		FROM email_templates` + whereClause + `
		ORDER BY name ASC`

	rows, err := r.db.Query(ctx, query, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get email templates: %w", err)
	}
	defer rows.Close()

	var templates []*models.EmailTemplate
	for rows.Next() {
		template := &models.EmailTemplate{}
		err := rows.Scan(
			&template.ID, &template.Name, &template.Subject, &template.HTMLContent,
			&template.TextContent, &template.Variables, &template.IsActive,
			&template.CreatedAt, &template.UpdatedAt,
		)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to scan email template: %w", err)
		}
		templates = append(templates, template)
	}

	return templates, totalCount, nil
}
