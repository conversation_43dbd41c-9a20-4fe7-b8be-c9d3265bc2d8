package clients

import (
	"context"

	threadpb "github.com/olzzhas/edunite-server/course_service/pb/thread"

	"google.golang.org/grpc"
)

type ThreadClient struct {
	client threadpb.ThreadServiceClient
}

func NewThreadClient(conn *grpc.ClientConn) *ThreadClient {
	return &ThreadClient{
		client: threadpb.NewThreadServiceClient(conn),
	}
}

// CreateThread sends a request to create a new thread
func (tc *ThreadClient) CreateThread(ctx context.Context, req *threadpb.ThreadRequest) (*threadpb.ThreadResponse, error) {
	resp, err := tc.client.CreateThread(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// ListThreadsByCourse sends a request to list threads by course ID
func (tc *ThreadClient) ListThreadsByCourse(ctx context.Context, req *threadpb.ThreadsByCourseRequest) (*threadpb.ThreadsResponse, error) {
	resp, err := tc.client.ListThreadsByCourse(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// ListThreads sends a request to list all threads
func (tc *ThreadClient) ListThreads(ctx context.Context, req *threadpb.ThreadEmptyRequest) (*threadpb.ThreadsResponse, error) {
	resp, err := tc.client.ListThreads(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// GetThreadByID sends a request to get a thread by ID
func (tc *ThreadClient) GetThreadByID(ctx context.Context, req *threadpb.ThreadByID) (*threadpb.ThreadResponse, error) {
	resp, err := tc.client.GetThreadByID(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// UpdateThreadByID sends a request to update a thread by ID
func (tc *ThreadClient) UpdateThreadByID(ctx context.Context, req *threadpb.ThreadUpdateRequest) (*threadpb.ThreadResponse, error) {
	resp, err := tc.client.UpdateThreadByID(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// DeleteThreadByID sends a request to delete a thread by ID
func (tc *ThreadClient) DeleteThreadByID(ctx context.Context, req *threadpb.ThreadByID) (*threadpb.ThreadEmptyResponse, error) {
	resp, err := tc.client.DeleteThreadByID(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// RegisterUserToThread регистрирует одного пользователя в потоке
func (tc *ThreadClient) RegisterUserToThread(ctx context.Context, req *threadpb.RegisterUserToThreadRequest) (*threadpb.ThreadEmptyResponse, error) {
	resp, err := tc.client.RegisterUserToThread(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// RegisterManyUsersToThread регистрирует нескольких пользователей в потоке
func (tc *ThreadClient) RegisterManyUsersToThread(ctx context.Context, req *threadpb.RegisterManyUsersToThreadRequest) (*threadpb.ThreadEmptyResponse, error) {
	resp, err := tc.client.RegisterManyUsersToThread(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// RemoveRegistrationToThread удаляет регистрацию одного пользователя из потока
func (tc *ThreadClient) RemoveRegistrationToThread(ctx context.Context, req *threadpb.RemoveRegistrationToThreadRequest) (*threadpb.ThreadEmptyResponse, error) {
	resp, err := tc.client.RemoveRegistrationToThread(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// RemoveManyRegistrationsToThread удаляет регистрации нескольких пользователей из потока
func (tc *ThreadClient) RemoveManyRegistrationsToThread(ctx context.Context, req *threadpb.RemoveManyRegistrationsToThreadRequest) (*threadpb.ThreadEmptyResponse, error) {
	resp, err := tc.client.RemoveManyRegistrationsToThread(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// ListThreadRegistrations получить список регистрации на потоки
func (tc *ThreadClient) ListThreadRegistrations(ctx context.Context, treadID int64) (*threadpb.ListThreadRegistrationsResponse, error) {
	resp, err := tc.client.ListThreadRegistrations(ctx, &threadpb.ThreadByID{ThreadId: treadID})
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// CreateWeek создать неделю
func (tc *ThreadClient) CreateWeek(ctx context.Context, req *threadpb.WeekRequest) (*threadpb.WeekResponse, error) {
	resp, err := tc.client.CreateWeek(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// UpdateWeek обновить неделю
func (tc *ThreadClient) UpdateWeek(ctx context.Context, req *threadpb.WeekUpdateRequest) (*threadpb.WeekResponse, error) {
	resp, err := tc.client.UpdateWeek(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// DeleteWeek удалить неделю
func (tc *ThreadClient) DeleteWeek(ctx context.Context, req *threadpb.WeekByID) (*threadpb.WeekResponse, error) {
	_, err := tc.client.DeleteWeekByID(ctx, req)
	if err != nil {
		return nil, err
	}
	return nil, nil
}

// GetWeekByID получить неделю по айди
func (tc *ThreadClient) GetWeekByID(ctx context.Context, req *threadpb.WeekByID) (*threadpb.WeekResponse, error) {
	resp, err := tc.client.GetWeekByID(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// ListWeeksByThread получить все недели по потоку
func (tc *ThreadClient) ListWeeksByThread(ctx context.Context, req *threadpb.WeeksForThreadRequest) (*threadpb.WeeksResponse, error) {
	resp, err := tc.client.ListWeeksForThread(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// CreateThreadSchedule creates a schedule template for a thread
func (tc *ThreadClient) CreateThreadSchedule(ctx context.Context, req *threadpb.ThreadScheduleRequest) (*threadpb.ThreadScheduleResponse, error) {
	return tc.client.CreateThreadSchedule(ctx, req)
}

// ListThreadSchedules returns all schedule templates for a thread
func (tc *ThreadClient) ListThreadSchedules(ctx context.Context, req *threadpb.ThreadSchedulesRequest) (*threadpb.ThreadSchedulesResponse, error) {
	return tc.client.ListThreadSchedules(ctx, req)
}

// GetThreadScheduleByID returns a single schedule template by ID
func (tc *ThreadClient) GetThreadScheduleByID(ctx context.Context, req *threadpb.ThreadScheduleByID) (*threadpb.ThreadScheduleResponse, error) {
	return tc.client.GetThreadScheduleByID(ctx, req)
}

// UpdateThreadSchedule updates an existing schedule template
func (tc *ThreadClient) UpdateThreadSchedule(ctx context.Context, req *threadpb.ThreadScheduleUpdateRequest) (*threadpb.ThreadScheduleResponse, error) {
	return tc.client.UpdateThreadSchedule(ctx, req)
}

// DeleteThreadScheduleByID deletes a schedule template by ID
func (tc *ThreadClient) DeleteThreadScheduleByID(ctx context.Context, req *threadpb.ThreadScheduleByID) (*threadpb.ThreadEmptyResponse, error) {
	return tc.client.DeleteThreadScheduleByID(ctx, req)
}

// UpdateScheduleLocation updates the location field for a schedule
func (tc *ThreadClient) UpdateScheduleLocation(ctx context.Context, scheduleID int64, location string) error {
	req := &threadpb.ThreadScheduleUpdateRequest{
		Id:       scheduleID,
		Location: location,
	}
	_, err := tc.client.UpdateThreadSchedule(ctx, req)
	return err
}

// GetScheduleLocation gets the location field for a schedule
func (tc *ThreadClient) GetScheduleLocation(ctx context.Context, scheduleID int64) (string, error) {
	// Get the schedule by ID
	req := &threadpb.ThreadScheduleByID{ScheduleId: scheduleID}
	resp, err := tc.client.GetThreadScheduleByID(ctx, req)
	if err != nil {
		return "", err
	}

	// Return the location field
	return resp.GetLocation(), nil
}

// ListThreadsForUser returns threads full info for user
func (tc *ThreadClient) ListThreadsForUser(ctx context.Context, req *threadpb.UserThreadsRequest) (*threadpb.UserThreadsResponse, error) {
	resp, err := tc.client.ListThreadsForUser(ctx, req)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

// ListThreadCourseGradesForUser returns only course name and final grade for user's threads
func (tc *ThreadClient) ListThreadCourseGradesForUser(ctx context.Context, req *threadpb.UserThreadsRequest) (*threadpb.UserThreadCourseGradesResponse, error) {
	resp, err := tc.client.ListThreadCourseGradesForUser(ctx, req)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

func (tc *ThreadClient) ListWeeksWithHomework(ctx context.Context, req *threadpb.WeeksWithHwRequest) (*threadpb.WeeksWithHwResponse, error) {
	resp, err := tc.client.ListWeeksWithHomework(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// CreateLocation creates a new location
func (tc *ThreadClient) CreateLocation(ctx context.Context, req *threadpb.LocationRequest) (*threadpb.LocationResponse, error) {
	return tc.client.CreateLocation(ctx, req)
}

// GetLocationByID gets a location by ID
func (tc *ThreadClient) GetLocationByID(ctx context.Context, locationID int64) (*threadpb.LocationResponse, error) {
	req := &threadpb.LocationByID{LocationId: locationID}
	return tc.client.GetLocationByID(ctx, req)
}

// ListLocations lists all locations
func (tc *ThreadClient) ListLocations(ctx context.Context) (*threadpb.LocationsResponse, error) {
	req := &threadpb.LocationEmptyRequest{}
	return tc.client.ListLocations(ctx, req)
}

// UpdateLocation updates an existing location
func (tc *ThreadClient) UpdateLocation(ctx context.Context, req *threadpb.LocationUpdateRequest) (*threadpb.LocationResponse, error) {
	return tc.client.UpdateLocation(ctx, req)
}

// DeleteLocation deletes a location by ID
func (tc *ThreadClient) DeleteLocation(ctx context.Context, locationID int64) (*threadpb.ThreadEmptyResponse, error) {
	req := &threadpb.LocationByID{LocationId: locationID}
	return tc.client.DeleteLocation(ctx, req)
}

// CheckLocationAvailability checks if a location is available during a specific time slot
func (tc *ThreadClient) CheckLocationAvailability(ctx context.Context, req *threadpb.LocationAvailabilityRequest) (*threadpb.LocationAvailabilityResponse, error) {
	return tc.client.CheckLocationAvailability(ctx, req)
}

// CheckPrerequisites checks if a student has completed all prerequisites for a course
func (tc *ThreadClient) CheckPrerequisites(ctx context.Context, userID, threadID int64) error {
	req := &threadpb.CheckPrerequisitesRequest{
		UserId:   userID,
		ThreadId: threadID,
	}
	_, err := tc.client.CheckPrerequisites(ctx, req)
	return err
}

// GradeFinalThreadRegistration grades a final thread registration
func (tc *ThreadClient) GradeFinalThreadRegistration(ctx context.Context, req *threadpb.GradeFinalThreadRegistrationRequest) (*threadpb.GradeFinalThreadRegistrationResponse, error) {
	resp, err := tc.client.GradeFinalThreadRegistration(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// ListAttendanceSessions returns all scheduled sessions for a thread where attendance can be marked
func (tc *ThreadClient) ListAttendanceSessions(ctx context.Context, req *threadpb.ThreadByID) (*threadpb.AttendanceSessionsResponse, error) {
	resp, err := tc.client.ListAttendanceSessions(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
