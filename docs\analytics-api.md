# Analytics API Documentation

## Обзор

API для получения аналитических данных и статистики платформы EduNite. Предоставляет данные для Dashboard и Analytics страниц.

## Базовый URL

```
http://localhost:8081/api/analytics
```

## Аутентификация

Все эндпоинты требуют аутентификации через Bearer токен в заголовке `Authorization`.

```
Authorization: Bearer <token>
```

## Эндпоинты

### 1. Dashboard Overview - Основные метрики

**GET** `/api/analytics/overview`

Возвращает основные метрики для dashboard.

#### Параметры запроса
- `period` (optional): Период для анализа
  - `last_7_days` - последние 7 дней
  - `last_30_days` - последние 30 дней (по умолчанию)
  - `last_90_days` - последние 90 дней

#### Пример запроса
```bash
curl -X GET "http://localhost:8081/api/analytics/overview?period=last_30_days" \
  -H "Authorization: Bearer <token>"
```

#### Пример ответа
```json
{
  "stats": {
    "totalUsers": {
      "value": 2318,
      "trend": "up",
      "trendValue": "+6.08%",
      "previousPeriodValue": 2185
    },
    "totalCourses": {
      "value": 156,
      "trend": "up", 
      "trendValue": "+15.03%",
      "previousPeriodValue": 135
    },
    "totalThreads": {
      "value": 3671,
      "trend": "down",
      "trendValue": "-0.03%",
      "previousPeriodValue": 3672
    },
    "activeStudents": {
      "value": 7265,
      "trend": "up",
      "trendValue": "+11.01%",
      "previousPeriodValue": 6540
    }
  },
  "period": "last_30_days"
}
```

### 2. Пользователи по времени (График)

**GET** `/api/analytics/users-timeline`

Возвращает данные для графика активности пользователей.

#### Параметры запроса
- `period` (optional): `7d`, `30d`, `90d`, `1y` (по умолчанию: `30d`)
- `granularity` (optional): `day`, `week`, `month` (по умолчанию: `day`)

#### Пример запроса
```bash
curl -X GET "http://localhost:8081/api/analytics/users-timeline?period=30d&granularity=day" \
  -H "Authorization: Bearer <token>"
```

#### Пример ответа
```json
{
  "timeline": [
    {
      "date": "2024-01-01",
      "totalUsers": 10000,
      "newUsers": 150,
      "activeUsers": 8500
    },
    {
      "date": "2024-01-02", 
      "totalUsers": 10150,
      "newUsers": 200,
      "activeUsers": 8700
    }
  ],
  "comparison": []
}
```

### 3. Статистика по устройствам и локациям

**GET** `/api/analytics/user-demographics`

Возвращает демографические данные пользователей.

#### Пример запроса
```bash
curl -X GET "http://localhost:8081/api/analytics/user-demographics" \
  -H "Authorization: Bearer <token>"
```

#### Пример ответа
```json
{
  "devices": [
    { "name": "Windows", "value": 30, "percentage": 30.0 },
    { "name": "Mac", "value": 25, "percentage": 25.0 },
    { "name": "Linux", "value": 15, "percentage": 15.0 },
    { "name": "iOS", "value": 20, "percentage": 20.0 },
    { "name": "Android", "value": 15, "percentage": 15.0 },
    { "name": "Other", "value": 5, "percentage": 5.0 }
  ],
  "locations": [
    { "name": "Kazakhstan", "value": 52.1, "percentage": 52.1 },
    { "name": "Russia", "value": 22.8, "percentage": 22.8 },
    { "name": "USA", "value": 13.9, "percentage": 13.9 },
    { "name": "Other", "value": 11.2, "percentage": 11.2 }
  ]
}
```

### 4. Последние активности

**GET** `/api/analytics/recent-activities`

Возвращает список последних активностей пользователей.

#### Параметры запроса
- `limit` (optional): Количество записей (по умолчанию: 10)
- `type` (optional): Тип активности
  - `enrollment` - регистрации на курсы
  - `assignment` - отправка заданий
  - `login` - входы в систему
  - `all` - все типы (по умолчанию)

#### Пример запроса
```bash
curl -X GET "http://localhost:8081/api/analytics/recent-activities?limit=5&type=all" \
  -H "Authorization: Bearer <token>"
```

#### Пример ответа
```json
{
  "activities": [
    {
      "id": "ACT-001",
      "type": "enrollment",
      "user": {
        "id": 123,
        "name": "John Doe",
        "email": "<EMAIL>"
      },
      "course": {
        "id": 45,
        "title": "Introduction to Computer Science"
      },
      "date": "2024-01-15T10:30:00Z",
      "status": "completed"
    }
  ]
}
```

### 5. Детальная статистика

**GET** `/api/analytics/detailed-stats`

Возвращает детальную аналитику для Analytics страницы.

#### Параметры запроса
- `period` (optional): `7d`, `30d`, `90d`, `1y`
- `metrics` (optional): Список метрик через запятую (`revenue`, `activity`, `performance`)

#### Пример запроса
```bash
curl -X GET "http://localhost:8081/api/analytics/detailed-stats?period=30d&metrics=activity,performance" \
  -H "Authorization: Bearer <token>"
```

### 6. Ключевые метрики производительности

**GET** `/api/analytics/performance-metrics`

Возвращает ключевые метрики производительности системы.

#### Пример запроса
```bash
curl -X GET "http://localhost:8081/api/analytics/performance-metrics" \
  -H "Authorization: Bearer <token>"
```

#### Пример ответа
```json
{
  "metrics": {
    "courseCompletionRate": {
      "value": 3.2,
      "unit": "%",
      "trend": "up",
      "change": "+0.5%",
      "description": "Course completion rate"
    },
    "averageGrade": {
      "value": 85.20,
      "unit": "points",
      "trend": "up", 
      "change": "+3.50",
      "description": "Average assignment grade"
    },
    "attendanceRate": {
      "value": 42.8,
      "unit": "%",
      "trend": "down",
      "change": "-2.1%", 
      "description": "Average attendance rate"
    },
    "averageSessionDuration": {
      "value": "2m 45s",
      "unit": "time",
      "trend": "up",
      "change": "+15s",
      "description": "Average session duration"
    }
  }
}
```

## Коды ошибок

### 400 Bad Request
```json
{
  "error": {
    "code": "INVALID_PARAMETER",
    "message": "Invalid limit parameter",
    "details": "Limit must be a valid integer"
  }
}
```

### 500 Internal Server Error
```json
{
  "error": {
    "code": "ANALYTICS_ERROR",
    "message": "Unable to fetch analytics data",
    "details": "Database connection failed"
  }
}
```

## Примечания

1. Все даты возвращаются в формате ISO 8601 (YYYY-MM-DDTHH:mm:ssZ)
2. Тренды могут быть: "up", "down", "neutral"
3. Процентные значения представлены как числа с плавающей точкой
4. Все эндпоинты поддерживают CORS для фронтенда
5. Некоторые данные (устройства, источники трафика) пока возвращают моковые значения
