// internal/database/threadquery/repo.go
package threadquery

import (
	"context"
	"fmt"

	"github.com/jackc/pgx/v4/pgxpool"
	"github.com/olzzhas/edunite-server/course_service/internal/dto"
)

// Repo отвечает только за «тяжёлые» SELECT‑ы thread‑домена.
type Repo struct{ db *pgxpool.Pool }

func New(db *pgxpool.Pool) *Repo { return &Repo{db: db} }

// ListThreadsForUser возвращает все потоки, на которые подписан user,
// вместе c курсом и семестром.
func (r *Repo) ListThreadsForUser(
	ctx context.Context, userID int64,
) ([]*dto.ThreadWithRelations, error) {

	const q = `
		SELECT
			  t.id, t.course_id, t.semester_id, t.teacher_id,
			  t.max_students, t.title, t.syllabus_url,
			  t.created_at, t.updated_at,

			  c.id, c.title, c.description, c.banner_image_url,

			  s.id, s.name, s.start_date, s.end_date,

			  tr.final_grade
		FROM            threads               AS t
		JOIN            thread_registrations  AS tr ON tr.thread_id = t.id
		JOIN            courses               AS c  ON c.id        = t.course_id
		JOIN            semesters             AS s  ON s.id        = t.semester_id
		WHERE           tr.user_id = $1
		ORDER BY        t.id;`

	rows, err := r.db.Query(ctx, q, userID)
	if err != nil {
		return nil, fmt.Errorf("list threads for user: %w", err)
	}
	defer rows.Close()

	var items []*dto.ThreadWithRelations
	for rows.Next() {
		var rel dto.ThreadWithRelations
		if err := rows.Scan(
			// ── thread
			&rel.Thread.ID, &rel.Thread.CourseID, &rel.Thread.SemesterID,
			&rel.Thread.TeacherID, &rel.Thread.MaxStudents,
			&rel.Thread.Title, &rel.Thread.SyllabusURL,
			&rel.Thread.CreatedAt, &rel.Thread.UpdatedAt,
			// ── course
			&rel.Course.ID, &rel.Course.Title,
			&rel.Course.Description, &rel.Course.BannerImageUrl,
			// ── semester
			&rel.Semester.ID, &rel.Semester.Name,
			&rel.Semester.StartDate, &rel.Semester.EndDate,
			// ── final grade
			&rel.FinalGrade,
		); err != nil {
			return nil, fmt.Errorf("scan: %w", err)
		}
		items = append(items, &rel)
	}
	return items, rows.Err()
}

// ListThreadCourseGradesForUser возвращает только название курса и финальную оценку
// для всех потоков, на которые подписан пользователь.
func (r *Repo) ListThreadCourseGradesForUser(
	ctx context.Context, userID int64,
) ([]*dto.ThreadCourseGrade, error) {

	const q = `
		SELECT
			  t.id,
			  c.title,
			  tr.final_grade
		FROM            threads               AS t
		JOIN            thread_registrations  AS tr ON tr.thread_id = t.id
		JOIN            courses               AS c  ON c.id        = t.course_id
		WHERE           tr.user_id = $1
		ORDER BY        t.id;`

	rows, err := r.db.Query(ctx, q, userID)
	if err != nil {
		return nil, fmt.Errorf("list thread course grades for user: %w", err)
	}
	defer rows.Close()

	var items []*dto.ThreadCourseGrade
	for rows.Next() {
		var item dto.ThreadCourseGrade
		if err := rows.Scan(
			&item.ThreadID,
			&item.CourseName,
			&item.FinalGrade,
		); err != nil {
			return nil, fmt.Errorf("scan: %w", err)
		}
		items = append(items, &item)
	}
	return items, rows.Err()
}
