package clients

import (
	"context"

	pb "github.com/olzzhas/edunite-server/notification_service/proto"
	"google.golang.org/grpc"
)

type NotificationClient struct {
	client pb.NotificationServiceClient
}

func NewNotificationClient(conn *grpc.ClientConn) *NotificationClient {
	return &NotificationClient{
		client: pb.NewNotificationServiceClient(conn),
	}
}

func (c *NotificationClient) CreateNotification(ctx context.Context, req *pb.CreateNotificationRequest) (*pb.NotificationResponse, error) {
	return c.client.CreateNotification(ctx, req)
}

func (c *NotificationClient) GetUserNotifications(ctx context.Context, req *pb.GetUserNotificationsRequest) (*pb.GetUserNotificationsResponse, error) {
	return c.client.GetUserNotifications(ctx, req)
}

func (c *NotificationClient) GetAllNotifications(ctx context.Context, req *pb.GetAllNotificationsRequest) (*pb.GetAllNotificationsResponse, error) {
	return c.client.GetAllNotifications(ctx, req)
}

func (c *NotificationClient) MarkAsRead(ctx context.Context, req *pb.MarkAsReadRequest) (*pb.MarkAsReadResponse, error) {
	return c.client.MarkAsRead(ctx, req)
}

func (c *NotificationClient) DeleteNotification(ctx context.Context, req *pb.DeleteNotificationRequest) (*pb.DeleteNotificationResponse, error) {
	return c.client.DeleteNotification(ctx, req)
}

func (c *NotificationClient) SendScheduledNotifications(ctx context.Context, req *pb.SendScheduledNotificationsRequest) (*pb.SendScheduledNotificationsResponse, error) {
	return c.client.SendScheduledNotifications(ctx, req)
}

func (c *NotificationClient) GetNotificationStats(ctx context.Context, req *pb.GetNotificationStatsRequest) (*pb.GetNotificationStatsResponse, error) {
	return c.client.GetNotificationStats(ctx, req)
}

func (c *NotificationClient) GetEmailTemplates(ctx context.Context, req *pb.GetEmailTemplatesRequest) (*pb.GetEmailTemplatesResponse, error) {
	return c.client.GetEmailTemplates(ctx, req)
}

func (c *NotificationClient) GetClient() pb.NotificationServiceClient {
	return c.client
}
