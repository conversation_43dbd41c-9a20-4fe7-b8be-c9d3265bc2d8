package grpc

import (
	"context"

	"github.com/olzzhas/edunite-server/notification_service/internal/models"
	"github.com/olzzhas/edunite-server/notification_service/internal/service"
	pb "github.com/olzzhas/edunite-server/notification_service/proto"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type NotificationServer struct {
	pb.UnimplementedNotificationServiceServer
	service service.NotificationService
}

func NewNotificationServer(service service.NotificationService) *NotificationServer {
	return &NotificationServer{
		service: service,
	}
}

func (s *NotificationServer) CreateNotification(ctx context.Context, req *pb.CreateNotificationRequest) (*pb.NotificationResponse, error) {
	// Convert protobuf request to internal model
	createReq := &models.CreateNotificationRequest{
		Title:      req.Title,
		Message:    req.Message,
		Type:       convertPBNotificationType(req.Type),
		Priority:   convertPBNotificationPriority(req.Priority),
		TargetType: convertPBTargetType(req.TargetType),
		SendEmail:  req.SendEmail,
	}

	if req.TargetValue != "" {
		createReq.TargetValue = &req.TargetValue
	}

	if req.SenderId != 0 {
		createReq.SenderID = &req.SenderId
	}

	if req.EmailSubject != "" {
		createReq.EmailSubject = &req.EmailSubject
	}

	if req.EmailTemplate != "" {
		createReq.EmailTemplate = &req.EmailTemplate
	}

	if req.ScheduledAt != nil {
		scheduledAt := req.ScheduledAt.AsTime()
		createReq.ScheduledAt = &scheduledAt
	}

	// Create notification
	notification, err := s.service.CreateNotification(ctx, createReq)
	if err != nil {
		return &pb.NotificationResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.NotificationResponse{
		Success:      true,
		Message:      "Notification created successfully",
		Notification: convertNotificationToPB(notification),
	}, nil
}

func (s *NotificationServer) GetUserNotifications(ctx context.Context, req *pb.GetUserNotificationsRequest) (*pb.GetUserNotificationsResponse, error) {
	page := req.Page
	if page <= 0 {
		page = 1
	}

	limit := req.Limit
	if limit <= 0 {
		limit = 20
	}

	notifications, totalCount, unreadCount, err := s.service.GetUserNotifications(
		ctx, req.UserId, page, limit, req.UnreadOnly)
	if err != nil {
		return nil, err
	}

	pbNotifications := make([]*pb.UserNotification, len(notifications))
	for i, notification := range notifications {
		pbNotifications[i] = &pb.UserNotification{
			Notification: convertNotificationToPB(notification.Notification),
			Recipient:    convertRecipientToPB(notification.Recipient),
		}
	}

	return &pb.GetUserNotificationsResponse{
		Notifications: pbNotifications,
		TotalCount:    totalCount,
		UnreadCount:   unreadCount,
	}, nil
}

func (s *NotificationServer) GetAllNotifications(ctx context.Context, req *pb.GetAllNotificationsRequest) (*pb.GetAllNotificationsResponse, error) {
	page := req.Page
	if page <= 0 {
		page = 1
	}

	limit := req.Limit
	if limit <= 0 {
		limit = 20
	}

	targetType := convertPBTargetType(req.TargetType)
	notifications, totalCount, err := s.service.GetAllNotifications(
		ctx, page, limit, targetType, req.TargetValue)
	if err != nil {
		return nil, err
	}

	pbNotifications := make([]*pb.Notification, len(notifications))
	for i, notification := range notifications {
		pbNotifications[i] = convertNotificationToPB(notification)
	}

	return &pb.GetAllNotificationsResponse{
		Notifications: pbNotifications,
		TotalCount:    totalCount,
	}, nil
}

func (s *NotificationServer) MarkAsRead(ctx context.Context, req *pb.MarkAsReadRequest) (*pb.MarkAsReadResponse, error) {
	err := s.service.MarkAsRead(ctx, req.NotificationId, req.UserId)
	if err != nil {
		return &pb.MarkAsReadResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.MarkAsReadResponse{
		Success: true,
		Message: "Notification marked as read",
	}, nil
}

func (s *NotificationServer) DeleteNotification(ctx context.Context, req *pb.DeleteNotificationRequest) (*pb.DeleteNotificationResponse, error) {
	err := s.service.DeleteNotification(ctx, req.NotificationId)
	if err != nil {
		return &pb.DeleteNotificationResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.DeleteNotificationResponse{
		Success: true,
		Message: "Notification deleted successfully",
	}, nil
}

func (s *NotificationServer) SendScheduledNotifications(ctx context.Context, req *pb.SendScheduledNotificationsRequest) (*pb.SendScheduledNotificationsResponse, error) {
	sentCount, err := s.service.SendScheduledNotifications(ctx)
	if err != nil {
		return &pb.SendScheduledNotificationsResponse{
			SentCount: 0,
			Message:   err.Error(),
		}, nil
	}

	return &pb.SendScheduledNotificationsResponse{
		SentCount: sentCount,
		Message:   "Scheduled notifications sent successfully",
	}, nil
}

func (s *NotificationServer) GetNotificationStats(ctx context.Context, req *pb.GetNotificationStatsRequest) (*pb.GetNotificationStatsResponse, error) {
	stats, err := s.service.GetNotificationStats(ctx, req.UserId)
	if err != nil {
		return nil, err
	}

	return &pb.GetNotificationStatsResponse{
		TotalNotifications:  stats.TotalNotifications,
		UnreadNotifications: stats.UnreadNotifications,
		ReadNotifications:   stats.ReadNotifications,
		EmailNotifications:  stats.EmailNotifications,
	}, nil
}

func (s *NotificationServer) GetEmailTemplates(ctx context.Context, req *pb.GetEmailTemplatesRequest) (*pb.GetEmailTemplatesResponse, error) {
	templates, totalCount, err := s.service.GetEmailTemplates(ctx, req.ActiveOnly)
	if err != nil {
		return nil, err
	}

	pbTemplates := make([]*pb.EmailTemplate, len(templates))
	for i, template := range templates {
		pbTemplates[i] = convertEmailTemplateToPB(template)
	}

	return &pb.GetEmailTemplatesResponse{
		Templates:  pbTemplates,
		TotalCount: totalCount,
	}, nil
}

// Helper functions for converting between protobuf and internal models

func convertNotificationToPB(n *models.Notification) *pb.Notification {
	pbNotification := &pb.Notification{
		Id:         n.ID,
		Title:      n.Title,
		Message:    n.Message,
		Type:       convertNotificationTypeToPB(n.Type),
		Priority:   convertNotificationPriorityToPB(n.Priority),
		TargetType: convertTargetTypeToPB(n.TargetType),
		SendEmail:  n.SendEmail,
		CreatedAt:  timestamppb.New(n.CreatedAt),
		UpdatedAt:  timestamppb.New(n.UpdatedAt),
	}

	if n.TargetValue != nil {
		pbNotification.TargetValue = *n.TargetValue
	}

	if n.SenderID != nil {
		pbNotification.SenderId = *n.SenderID
	}

	if n.EmailSubject != nil {
		pbNotification.EmailSubject = *n.EmailSubject
	}

	if n.EmailTemplate != nil {
		pbNotification.EmailTemplate = *n.EmailTemplate
	}

	if n.ScheduledAt != nil {
		pbNotification.ScheduledAt = timestamppb.New(*n.ScheduledAt)
	}

	if n.SentAt != nil {
		pbNotification.SentAt = timestamppb.New(*n.SentAt)
	}

	return pbNotification
}

func convertRecipientToPB(r *models.NotificationRecipient) *pb.NotificationRecipient {
	pbRecipient := &pb.NotificationRecipient{
		Id:             r.ID,
		NotificationId: r.NotificationID,
		UserId:         r.UserID,
		IsRead:         r.IsRead,
		EmailSent:      r.EmailSent,
		CreatedAt:      timestamppb.New(r.CreatedAt),
		UpdatedAt:      timestamppb.New(r.UpdatedAt),
	}

	if r.ReadAt != nil {
		pbRecipient.ReadAt = timestamppb.New(*r.ReadAt)
	}

	if r.EmailSentAt != nil {
		pbRecipient.EmailSentAt = timestamppb.New(*r.EmailSentAt)
	}

	return pbRecipient
}

// Type conversion functions
func convertPBNotificationType(pbType pb.NotificationType) models.NotificationType {
	switch pbType {
	case pb.NotificationType_INFO:
		return models.NotificationTypeInfo
	case pb.NotificationType_WARNING:
		return models.NotificationTypeWarning
	case pb.NotificationType_SUCCESS:
		return models.NotificationTypeSuccess
	case pb.NotificationType_ERROR:
		return models.NotificationTypeError
	case pb.NotificationType_ANNOUNCEMENT:
		return models.NotificationTypeAnnouncement
	default:
		return models.NotificationTypeInfo
	}
}

func convertNotificationTypeToPB(modelType models.NotificationType) pb.NotificationType {
	switch modelType {
	case models.NotificationTypeInfo:
		return pb.NotificationType_INFO
	case models.NotificationTypeWarning:
		return pb.NotificationType_WARNING
	case models.NotificationTypeSuccess:
		return pb.NotificationType_SUCCESS
	case models.NotificationTypeError:
		return pb.NotificationType_ERROR
	case models.NotificationTypeAnnouncement:
		return pb.NotificationType_ANNOUNCEMENT
	default:
		return pb.NotificationType_INFO
	}
}

func convertPBNotificationPriority(pbPriority pb.NotificationPriority) models.NotificationPriority {
	switch pbPriority {
	case pb.NotificationPriority_LOW:
		return models.NotificationPriorityLow
	case pb.NotificationPriority_NORMAL:
		return models.NotificationPriorityNormal
	case pb.NotificationPriority_HIGH:
		return models.NotificationPriorityHigh
	case pb.NotificationPriority_URGENT:
		return models.NotificationPriorityUrgent
	default:
		return models.NotificationPriorityNormal
	}
}

func convertNotificationPriorityToPB(modelPriority models.NotificationPriority) pb.NotificationPriority {
	switch modelPriority {
	case models.NotificationPriorityLow:
		return pb.NotificationPriority_LOW
	case models.NotificationPriorityNormal:
		return pb.NotificationPriority_NORMAL
	case models.NotificationPriorityHigh:
		return pb.NotificationPriority_HIGH
	case models.NotificationPriorityUrgent:
		return pb.NotificationPriority_URGENT
	default:
		return pb.NotificationPriority_NORMAL
	}
}

func convertPBTargetType(pbTargetType pb.TargetType) models.TargetType {
	switch pbTargetType {
	case pb.TargetType_ALL:
		return models.TargetTypeAll
	case pb.TargetType_ROLE:
		return models.TargetTypeRole
	case pb.TargetType_USER:
		return models.TargetTypeUser
	case pb.TargetType_DEGREE:
		return models.TargetTypeDegree
	case pb.TargetType_COURSE:
		return models.TargetTypeCourse
	case pb.TargetType_THREAD:
		return models.TargetTypeThread
	default:
		return models.TargetTypeAll
	}
}

func convertTargetTypeToPB(modelTargetType models.TargetType) pb.TargetType {
	switch modelTargetType {
	case models.TargetTypeAll:
		return pb.TargetType_ALL
	case models.TargetTypeRole:
		return pb.TargetType_ROLE
	case models.TargetTypeUser:
		return pb.TargetType_USER
	case models.TargetTypeDegree:
		return pb.TargetType_DEGREE
	case models.TargetTypeCourse:
		return pb.TargetType_COURSE
	case models.TargetTypeThread:
		return pb.TargetType_THREAD
	default:
		return pb.TargetType_ALL
	}
}

func convertEmailTemplateToPB(template *models.EmailTemplate) *pb.EmailTemplate {
	pbTemplate := &pb.EmailTemplate{
		Id:          template.ID,
		Name:        template.Name,
		Subject:     template.Subject,
		HtmlContent: template.HTMLContent,
		IsActive:    template.IsActive,
		CreatedAt:   timestamppb.New(template.CreatedAt),
		UpdatedAt:   timestamppb.New(template.UpdatedAt),
	}

	if template.TextContent != nil {
		pbTemplate.TextContent = *template.TextContent
	}

	if template.Variables != nil {
		pbTemplate.Variables = *template.Variables
	}

	return pbTemplate
}
