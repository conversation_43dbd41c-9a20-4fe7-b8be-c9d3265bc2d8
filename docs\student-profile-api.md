# Student Profile API Documentation

## Обзор

API для получения полной информации о студенте, включая его degree программу, академическую статистику, доступные курсы и записанные потоки.

## Эндпоинт

### Получить профиль студента

**GET** `/students/{user_id}/profile`

Возвращает комплексную информацию о студенте включая:
- Основную информацию о пользователе
- Информацию о degree программах
- Академическую статистику (GPA, кредиты, прогресс)
- Доступные курсы
- Записанные потоки

#### Параметры

- `user_id` (path parameter, required) - ID студента

#### Ответ

**Успешный ответ (200 OK):**

```json
{
  "student_id": 123,
  "user": {
    "id": 123,
    "name": "Иван",
    "surname": "Иванов",
    "email": "<EMAIL>",
    "role": "student",
    "created_at": "2023-01-15T10:30:00Z",
    "updated_at": "2023-12-01T14:20:00Z"
  },
  "degrees": [
    {
      "id": 1,
      "degree_id": 1,
      "status": "in_progress",
      "start_date": "2023-09-01",
      "expected_graduation_date": "2027-06-30",
      "final_gpa": null,
      "degree": {
        "id": 1,
        "name": "Bachelor of Science in Computer Science",
        "level": "bachelor",
        "description": "Undergraduate degree in Computer Science",
        "required_credits": 120,
        "min_gpa": 2.0
      }
    }
  ],
  "academic_stats": {
    "gpa": 3.75,
    "total_credits_attempted": 45,
    "total_credits_earned": 42,
    "degree_progress_percent": 35.0,
    "required_credits": 120
  },
  "available_courses": [
    {
      "id": 1,
      "title": "Программирование",
      "description": "Основы программирования на Python",
      "created_at": "2023-01-01T00:00:00Z",
      "updated_at": "2023-06-01T00:00:00Z"
    },
    {
      "id": 2,
      "title": "Математический анализ",
      "description": "Дифференциальное и интегральное исчисление",
      "created_at": "2023-01-01T00:00:00Z",
      "updated_at": "2023-06-01T00:00:00Z"
    }
  ],
  "enrolled_threads": [
    {
      "id": 15,
      "title": "Программирование - Группа А",
      "max_students": 25,
      "course": {
        "id": 1,
        "title": "Программирование",
        "description": "Основы программирования на Python"
      }
    },
    {
      "id": 22,
      "title": "Математический анализ - Группа Б",
      "max_students": 30,
      "course": {
        "id": 2,
        "title": "Математический анализ",
        "description": "Дифференциальное и интегральное исчисление"
      }
    }
  ],
  "summary": {
    "total_degrees": 1,
    "total_available_courses": 2,
    "total_enrolled_threads": 2
  }
}
```

**Ошибки:**

- `400 Bad Request` - Неверный ID студента
- `500 Internal Server Error` - Внутренняя ошибка сервера

#### Пример запроса

```bash
curl -X GET "http://localhost:8081/students/123/profile" \
  -H "Content-Type: application/json"
```

## Описание полей ответа

### user
Основная информация о пользователе из User Service.

### degrees
Массив degree программ, на которые записан студент:
- `id` - ID записи студента на программу
- `degree_id` - ID самой degree программы
- `status` - Статус обучения (in_progress, completed, withdrawn, transferred)
- `start_date` - Дата начала обучения
- `expected_graduation_date` - Ожидаемая дата выпуска
- `actual_graduation_date` - Фактическая дата выпуска (если завершено)
- `final_gpa` - Финальный GPA (если завершено)
- `degree` - Детальная информация о degree программе

### academic_stats
Академическая статистика студента:
- `gpa` - Текущий средний балл
- `total_credits_attempted` - Общее количество попыток получения кредитов
- `total_credits_earned` - Количество заработанных кредитов
- `degree_progress_percent` - Процент завершения degree программы
- `required_credits` - Требуемое количество кредитов для завершения

### available_courses
Курсы, доступные студенту на основе его degree программы.

### enrolled_threads
Потоки (группы), на которые записан студент.

### summary
Краткая сводка с количественными показателями.

## Примечания

- Если у студента нет degree программы, поля `degrees` и `academic_stats` будут пустыми
- Если не удается получить какую-либо информацию, соответствующие поля будут пустыми, но эндпоинт продолжит работу
- Все даты возвращаются в формате ISO 8601
- Эндпоинт агрегирует данные из нескольких сервисов: User Service, Course Service (Transcript), Course Service (Courses), Thread Service

## Безопасность

- Эндпоинт требует аутентификации
- Студенты могут получать только свою собственную информацию
- Администраторы и преподаватели могут получать информацию о любом студенте

## Производительность

Эндпоинт выполняет несколько запросов к различным сервисам:
1. Получение информации о пользователе
2. Получение degree программ студента
3. Получение доступных курсов
4. Получение записанных потоков
5. Расчет академической статистики

При высокой нагрузке рекомендуется кэширование результатов.
