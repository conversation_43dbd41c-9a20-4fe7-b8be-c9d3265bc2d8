package handlers

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	assignmentpb "github.com/olzzhas/edunite-server/course_service/pb/assignment"
	"github.com/olzzhas/edunite-server/gateway/clients"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// AssignmentHandler handles HTTP requests for assignments
type AssignmentHandler struct {
	AssignmentService  *clients.AssignmentClient
	UserService        *clients.UserClient
	StorageService     *clients.StorageClient
	RabbitLogPublisher clients.LogPublisher
}

// --- Group Handlers ---

func (h *AssignmentHandler) CreateAssignmentGroup(c *gin.Context) {
	var input struct {
		ThreadId  int64   `json:"thread_id" binding:"required"`
		Name      string  `json:"name" binding:"required"`
		GroupType string  `json:"group_type" binding:"required"`
		Weight    float32 `json:"weight" binding:"required"`
	}
	if err := c.<PERSON>ind<PERSON>(&input); err != nil {
		c.<PERSON>(http.StatusBadRequest, gin.H{"error": err.<PERSON>rror()})
		return
	}
	resp, err := h.AssignmentService.CreateAssignmentGroup(c.Request.Context(), input.ThreadId, input.Name, input.GroupType, input.Weight)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusCreated, resp)
}

func (h *AssignmentHandler) ListAssignmentGroupsForThread(c *gin.Context) {
	threadID, err := strconv.ParseInt(c.Param("threadId"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid thread ID"})
		return
	}
	list, err := h.AssignmentService.ListAssignmentGroupsForThread(c.Request.Context(), threadID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, list)
}

func (h *AssignmentHandler) GetAssignmentGroupByID(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid group ID"})
		return
	}
	resp, err := h.AssignmentService.GetAssignmentGroupByID(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, resp)
}

func (h *AssignmentHandler) UpdateAssignmentGroupByID(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid group ID"})
		return
	}
	var input struct {
		ThreadId  int64   `json:"thread_id" binding:"required"`
		Name      string  `json:"name" binding:"required"`
		GroupType string  `json:"group_type" binding:"required"`
		Weight    float32 `json:"weight" binding:"required"`
	}
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	resp, err := h.AssignmentService.UpdateAssignmentGroupByID(c.Request.Context(), id, input.ThreadId, input.Name, input.GroupType, input.Weight)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, resp)
}

func (h *AssignmentHandler) DeleteAssignmentGroupByID(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid group ID"})
		return
	}
	if err := h.AssignmentService.DeleteAssignmentGroupByID(c.Request.Context(), id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.Status(http.StatusNoContent)
}

// --- Attachment Handlers ---

func (h *AssignmentHandler) CreateAssignmentAttachment(c *gin.Context) {
	assignmentID, err := strconv.ParseInt(c.Param("assignmentId"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid assignment ID"})
		return
	}
	var input struct {
		FileUrl string `json:"file_url" binding:"required"`
	}
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	resp, err := h.AssignmentService.CreateAssignmentAttachment(c.Request.Context(), assignmentID, input.FileUrl)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusCreated, resp)
}

func (h *AssignmentHandler) GetAssignmentAttachmentsByAssignmentID(c *gin.Context) {
	assignmentID, err := strconv.ParseInt(c.Param("assignmentId"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid assignment ID"})
		return
	}
	list, err := h.AssignmentService.GetAssignmentAttachmentsByAssignmentID(c.Request.Context(), assignmentID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, list)
}

func (h *AssignmentHandler) DeleteAssignmentAttachmentByID(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid attachment ID"})
		return
	}
	if err := h.AssignmentService.DeleteAssignmentAttachmentByID(c.Request.Context(), id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.Status(http.StatusNoContent)
}

// --- Submission Handlers ---

func (h *AssignmentHandler) CreateAssignmentSubmission(c *gin.Context) {
	assignmentID, err := strconv.ParseInt(c.Param("assignmentId"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid assignment ID"})
		return
	}

	// Check if this is a multipart form (with file) or JSON request
	contentType := c.Request.Header.Get("Content-Type")
	if strings.HasPrefix(contentType, "multipart/form-data") {
		// Handle multipart form with file upload
		h.createAssignmentSubmissionWithFile(c, assignmentID)
	} else {
		// Handle JSON request (original behavior)
		h.createAssignmentSubmissionJSON(c, assignmentID)
	}
}

// createAssignmentSubmissionJSON handles JSON requests for creating submissions (original behavior)
func (h *AssignmentHandler) createAssignmentSubmissionJSON(c *gin.Context, assignmentID int64) {
	var input struct {
		UserId   int64    `json:"user_id" binding:"required"`
		FileUrls []string `json:"file_urls" binding:"required"`
		Comment  string   `json:"comment"`
	}
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Check if this is an update to an existing submission or a new one
	resp, err := h.AssignmentService.CreateAssignmentSubmission(c.Request.Context(), assignmentID, input.UserId, input.FileUrls, input.Comment)
	if err != nil {
		// Check if this is a unique constraint violation (user already has a submission)
		if strings.Contains(err.Error(), "user already has a submission") {
			c.JSON(http.StatusConflict, gin.H{
				"error": "You have already submitted this assignment. Please use the update endpoint instead.",
				"code":  "ALREADY_SUBMITTED",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, resp)
}

// createAssignmentSubmissionWithFile handles multipart form requests with file uploads for submissions
func (h *AssignmentHandler) createAssignmentSubmissionWithFile(c *gin.Context, assignmentID int64) {
	// Log the form data for debugging
	h.RabbitLogPublisher.PublishLog(
		"info",
		"Received submission form data",
		"submission_create_with_file",
		map[string]any{
			"form_values":   c.Request.Form,
			"content_type":  c.Request.Header.Get("Content-Type"),
			"assignment_id": assignmentID,
		},
	)

	// Parse form fields
	userIDStr := c.PostForm("user_id")
	if userIDStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "user_id is required"})
		return
	}

	comment := c.PostForm("comment")

	// Parse user ID
	userID, err := strconv.ParseInt(userIDStr, 10, 64)
	if err != nil || userID <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid user_id"})
		return
	}

	// Check if there's a file to upload
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "file is required"})
		return
	}

	// We have a file to upload
	defer file.Close()

	// Check file size
	fileSize := header.Size
	if fileSize > 10*1024*1024 { // 10MB limit
		c.JSON(http.StatusBadRequest, gin.H{"error": "file size exceeds 10MB limit"})
		return
	}

	// Get content type
	contentType := header.Header.Get("Content-Type")
	if contentType == "" {
		contentType = "application/octet-stream"
	}

	// Read file content
	fileData := make([]byte, fileSize)
	_, err = file.Read(fileData)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error reading submission file: %v", err),
			"submission_create_with_file",
			map[string]any{
				"assignment_id": assignmentID,
				"user_id":       userID,
				"error":         err.Error(),
			},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to read file"})
		return
	}

	// Process filename
	originalFilename := header.Filename
	safeFilename := strings.ReplaceAll(originalFilename, " ", "_")
	objectName := fmt.Sprintf("%d_%s", time.Now().UnixNano(), safeFilename)

	// Upload file to storage
	_, err = h.StorageService.UploadFile(
		c.Request.Context(),
		"submission-files",
		objectName,
		contentType,
		fileData,
	)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error uploading submission file: %v", err),
			"submission_create_with_file",
			map[string]any{
				"assignment_id": assignmentID,
				"user_id":       userID,
				"error":         err.Error(),
			},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to upload file"})
		return
	}

	// Create file URL - always use just the bucket and filename, not the full URL with host
	fileUrl := fmt.Sprintf("submission-files/%s", objectName)

	// Create submission with the file URL
	fileUrls := []string{fileUrl}

	// Check for additional files
	for i := 2; ; i++ {
		additionalFile, additionalHeader, err := c.Request.FormFile(fmt.Sprintf("file%d", i))
		if err != nil {
			// No more files
			break
		}
		defer additionalFile.Close()

		// Process additional file
		additionalFileSize := additionalHeader.Size
		if additionalFileSize > 10*1024*1024 { // 10MB limit
			c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("file%d size exceeds 10MB limit", i)})
			return
		}

		// Get content type
		additionalContentType := additionalHeader.Header.Get("Content-Type")
		if additionalContentType == "" {
			additionalContentType = "application/octet-stream"
		}

		// Read file content
		additionalFileData := make([]byte, additionalFileSize)
		_, err = additionalFile.Read(additionalFileData)
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error reading additional submission file: %v", err),
				"submission_create_with_file",
				map[string]any{
					"assignment_id": assignmentID,
					"user_id":       userID,
					"file_number":   i,
					"error":         err.Error(),
				},
			)
			c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("failed to read file%d", i)})
			return
		}

		// Process filename
		additionalOriginalFilename := additionalHeader.Filename
		additionalSafeFilename := strings.ReplaceAll(additionalOriginalFilename, " ", "_")
		additionalObjectName := fmt.Sprintf("%d_%s", time.Now().UnixNano(), additionalSafeFilename)

		// Upload file to storage
		_, err = h.StorageService.UploadFile(
			c.Request.Context(),
			"submission-files",
			additionalObjectName,
			additionalContentType,
			additionalFileData,
		)
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error uploading additional submission file: %v", err),
				"submission_create_with_file",
				map[string]any{
					"assignment_id": assignmentID,
					"user_id":       userID,
					"file_number":   i,
					"error":         err.Error(),
				},
			)
			c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("failed to upload file%d", i)})
			return
		}

		// Create file URL - always use just the bucket and filename, not the full URL with host
		additionalFileUrl := fmt.Sprintf("submission-files/%s", additionalObjectName)

		// Add to file URLs
		fileUrls = append(fileUrls, additionalFileUrl)
	}

	// Create the submission
	resp, err := h.AssignmentService.CreateAssignmentSubmission(
		c.Request.Context(),
		assignmentID,
		userID,
		fileUrls,
		comment,
	)
	if err != nil {
		// Check if this is a unique constraint violation (user already has a submission)
		if strings.Contains(err.Error(), "user already has a submission") {
			c.JSON(http.StatusConflict, gin.H{
				"error": "You have already submitted this assignment. Please use the update endpoint instead.",
				"code":  "ALREADY_SUBMITTED",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, resp)
}

func (h *AssignmentHandler) GetAssignmentSubmissionByID(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid submission ID"})
		return
	}
	resp, err := h.AssignmentService.GetAssignmentSubmissionByID(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, resp)
}

func (h *AssignmentHandler) ListAssignmentSubmissionsByAssignmentID(c *gin.Context) {
	assignmentID, err := strconv.ParseInt(c.Param("assignmentId"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid assignment ID"})
		return
	}

	// Get submissions for the assignment
	submissions, err := h.AssignmentService.ListAssignmentSubmissionsByAssignmentID(c.Request.Context(), assignmentID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Enhance submissions with user information
	enhancedSubmissions := make([]map[string]interface{}, 0, len(submissions))

	for _, submission := range submissions {
		// Get user information for each submission
		user, err := h.UserService.GetUserWithContext(c.Request.Context(), submission.UserId)
		if err != nil {
			// Log the error but continue processing other submissions
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error fetching user info for submission: %v", err),
				"list_submissions",
				map[string]any{
					"submission_id": submission.Id,
					"user_id":       submission.UserId,
					"error":         err.Error(),
				},
			)

			// Add submission without user info
			enhancedSubmissions = append(enhancedSubmissions, map[string]interface{}{
				"id":            submission.Id,
				"assignment_id": submission.AssignmentId,
				"user_id":       submission.UserId,
				"submitted_at":  submission.SubmittedAt.AsTime().Format(time.RFC3339),
				"file_urls":     submission.FileUrls,
				"comment":       submission.Comment,
				"score":         submission.Score,
				"feedback":      submission.Feedback,
				"created_at":    submission.CreatedAt.AsTime().Format(time.RFC3339),
				"updated_at":    submission.UpdatedAt.AsTime().Format(time.RFC3339),
				"user":          nil, // User info not available
			})
			continue
		}

		// Add submission with user info
		enhancedSubmissions = append(enhancedSubmissions, map[string]interface{}{
			"id":            submission.Id,
			"assignment_id": submission.AssignmentId,
			"user_id":       submission.UserId,
			"submitted_at":  submission.SubmittedAt.AsTime().Format(time.RFC3339),
			"file_urls":     submission.FileUrls,
			"comment":       submission.Comment,
			"score":         submission.Score,
			"feedback":      submission.Feedback,
			"created_at":    submission.CreatedAt.AsTime().Format(time.RFC3339),
			"updated_at":    submission.UpdatedAt.AsTime().Format(time.RFC3339),
			"user": map[string]interface{}{
				"id":      user.Id,
				"name":    user.Name,
				"surname": user.Surname,
				"email":   user.Email,
				"role":    user.Role,
			},
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"assignment_id": assignmentID,
		"count":         len(enhancedSubmissions),
		"submissions":   enhancedSubmissions,
	})
}

// UpdateAssignmentSubmission updates an existing submission
func (h *AssignmentHandler) UpdateAssignmentSubmission(c *gin.Context) {
	submissionID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid submission ID"})
		return
	}

	// Check if this is a multipart form (with file) or JSON request
	contentType := c.Request.Header.Get("Content-Type")
	if strings.HasPrefix(contentType, "multipart/form-data") {
		// Handle multipart form with file upload
		h.updateAssignmentSubmissionWithFile(c, submissionID)
	} else {
		// Handle JSON request (original behavior)
		h.updateAssignmentSubmissionJSON(c, submissionID)
	}
}

// updateAssignmentSubmissionJSON handles JSON requests for updating submissions (original behavior)
func (h *AssignmentHandler) updateAssignmentSubmissionJSON(c *gin.Context, submissionID int64) {
	var input struct {
		FileUrls []string `json:"file_urls" binding:"required"`
		Comment  string   `json:"comment"`
	}

	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get the existing submission first
	existingSubmission, err := h.AssignmentService.GetAssignmentSubmissionByID(c.Request.Context(), submissionID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Submission not found"})
		return
	}

	// Update the submission
	resp, err := h.AssignmentService.UpdateAssignmentSubmission(
		c.Request.Context(),
		submissionID,
		existingSubmission.GetAssignmentId(),
		existingSubmission.GetUserId(),
		input.FileUrls,
		input.Comment,
	)

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// updateAssignmentSubmissionWithFile handles multipart form requests with file uploads for updating submissions
func (h *AssignmentHandler) updateAssignmentSubmissionWithFile(c *gin.Context, submissionID int64) {
	// Log the form data for debugging
	h.RabbitLogPublisher.PublishLog(
		"info",
		"Received submission update form data",
		"submission_update_with_file",
		map[string]any{
			"form_values":   c.Request.Form,
			"content_type":  c.Request.Header.Get("Content-Type"),
			"submission_id": submissionID,
		},
	)

	// Get the existing submission first
	existingSubmission, err := h.AssignmentService.GetAssignmentSubmissionByID(c.Request.Context(), submissionID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Submission not found"})
		return
	}

	// Parse form fields
	comment := c.PostForm("comment")

	// Check if there's a file to upload
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "file is required"})
		return
	}

	// We have a file to upload
	defer file.Close()

	// Check file size
	fileSize := header.Size
	if fileSize > 10*1024*1024 { // 10MB limit
		c.JSON(http.StatusBadRequest, gin.H{"error": "file size exceeds 10MB limit"})
		return
	}

	// Get content type
	contentType := header.Header.Get("Content-Type")
	if contentType == "" {
		contentType = "application/octet-stream"
	}

	// Read file content
	fileData := make([]byte, fileSize)
	_, err = file.Read(fileData)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error reading submission file: %v", err),
			"submission_update_with_file",
			map[string]any{
				"submission_id": submissionID,
				"error":         err.Error(),
			},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to read file"})
		return
	}

	// Process filename
	originalFilename := header.Filename
	safeFilename := strings.ReplaceAll(originalFilename, " ", "_")
	objectName := fmt.Sprintf("%d_%s", time.Now().UnixNano(), safeFilename)

	// Upload file to storage
	_, err = h.StorageService.UploadFile(
		c.Request.Context(),
		"submission-files",
		objectName,
		contentType,
		fileData,
	)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error uploading submission file: %v", err),
			"submission_update_with_file",
			map[string]any{
				"submission_id": submissionID,
				"error":         err.Error(),
			},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to upload file"})
		return
	}

	// Create file URL - always use just the bucket and filename, not the full URL with host
	fileUrl := fmt.Sprintf("submission-files/%s", objectName)

	// Create submission with the file URL
	fileUrls := []string{fileUrl}

	// Check for additional files
	for i := 2; ; i++ {
		additionalFile, additionalHeader, err := c.Request.FormFile(fmt.Sprintf("file%d", i))
		if err != nil {
			// No more files
			break
		}
		defer additionalFile.Close()

		// Process additional file
		additionalFileSize := additionalHeader.Size
		if additionalFileSize > 10*1024*1024 { // 10MB limit
			c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("file%d size exceeds 10MB limit", i)})
			return
		}

		// Get content type
		additionalContentType := additionalHeader.Header.Get("Content-Type")
		if additionalContentType == "" {
			additionalContentType = "application/octet-stream"
		}

		// Read file content
		additionalFileData := make([]byte, additionalFileSize)
		_, err = additionalFile.Read(additionalFileData)
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error reading additional submission file: %v", err),
				"submission_update_with_file",
				map[string]any{
					"submission_id": submissionID,
					"file_number":   i,
					"error":         err.Error(),
				},
			)
			c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("failed to read file%d", i)})
			return
		}

		// Process filename
		additionalOriginalFilename := additionalHeader.Filename
		additionalSafeFilename := strings.ReplaceAll(additionalOriginalFilename, " ", "_")
		additionalObjectName := fmt.Sprintf("%d_%s", time.Now().UnixNano(), additionalSafeFilename)

		// Upload file to storage
		_, err = h.StorageService.UploadFile(
			c.Request.Context(),
			"submission-files",
			additionalObjectName,
			additionalContentType,
			additionalFileData,
		)
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error uploading additional submission file: %v", err),
				"submission_update_with_file",
				map[string]any{
					"submission_id": submissionID,
					"file_number":   i,
					"error":         err.Error(),
				},
			)
			c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("failed to upload file%d", i)})
			return
		}

		// Create file URL - always use just the bucket and filename, not the full URL with host
		additionalFileUrl := fmt.Sprintf("submission-files/%s", additionalObjectName)

		// Add to file URLs
		fileUrls = append(fileUrls, additionalFileUrl)
	}

	// Update the submission
	resp, err := h.AssignmentService.UpdateAssignmentSubmission(
		c.Request.Context(),
		submissionID,
		existingSubmission.GetAssignmentId(),
		existingSubmission.GetUserId(),
		fileUrls,
		comment,
	)

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssignmentHandler) UpdateAssignmentSubmissionScore(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid submission ID"})
		return
	}
	var input struct {
		Score    int32  `json:"score" binding:"required"`
		Feedback string `json:"feedback" binding:"required"`
	}
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	resp, err := h.AssignmentService.UpdateAssignmentSubmissionScore(c.Request.Context(), id, input.Score, input.Feedback)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, resp)
}

func (h *AssignmentHandler) DeleteAssignmentSubmissionByID(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid submission ID"})
		return
	}
	if err := h.AssignmentService.DeleteAssignmentSubmissionByID(c.Request.Context(), id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.Status(http.StatusNoContent)
}

// CreateAssignmentHandler создаёт новую задачу
// POST /weeks/:weekId/assignments
func (h *AssignmentHandler) CreateAssignmentHandler(c *gin.Context) {
	weekID, err := strconv.ParseInt(c.Param("weekId"), 10, 64)
	if err != nil || weekID <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid weekId"})
		return
	}

	// Check if this is a multipart form (with file) or JSON request
	contentType := c.Request.Header.Get("Content-Type")
	if strings.HasPrefix(contentType, "multipart/form-data") {
		// Handle multipart form with file upload
		h.createAssignmentWithFile(c, weekID)
	} else {
		// Handle JSON request (original behavior)
		h.createAssignmentJSON(c, weekID)
	}
}

// createAssignmentJSON handles JSON requests for creating assignments (original behavior)
func (h *AssignmentHandler) createAssignmentJSON(c *gin.Context, weekID int64) {
	var input struct {
		Title             string `json:"title" binding:"required"`
		Description       string `json:"description"`
		DueDate           string `json:"due_date"`   // RFC3339, required only for task type
		MaxPoints         int32  `json:"max_points"` // Required only for task type
		AssignmentGroupId int64  `json:"assignment_group_id"`
		AssignmentType    string `json:"type" binding:"required"`
	}
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate based on assignment type
	if input.AssignmentType == "task" {
		// For task type, due_date and max_points are required
		if input.DueDate == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "due_date is required for task type"})
			return
		}
		if input.MaxPoints <= 0 {
			c.JSON(http.StatusBadRequest, gin.H{"error": "max_points must be > 0 for task type"})
			return
		}
	} else if input.AssignmentType != "info" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "type must be either 'task' or 'info'"})
		return
	}

	// Parse date if provided
	var dt time.Time
	if input.DueDate != "" {
		parsedDt, parseErr := time.Parse(time.RFC3339, input.DueDate)
		if parseErr != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid due_date format, use RFC3339"})
			return
		}
		dt = parsedDt
	} else {
		// For info type without due_date, set a far future date
		dt = time.Now().AddDate(10, 0, 0) // 10 years in the future
	}

	// gRPC вызов
	resp, err := h.AssignmentService.CreateAssignment(
		c.Request.Context(),
		weekID,
		input.Title,
		input.Description,
		timestamppb.New(dt),
		input.MaxPoints,
		input.AssignmentGroupId,
		input.AssignmentType,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, resp)
}

// createAssignmentWithFile handles multipart form requests with file uploads
func (h *AssignmentHandler) createAssignmentWithFile(c *gin.Context, weekID int64) {
	// Log the form data for debugging
	h.RabbitLogPublisher.PublishLog(
		"info",
		"Received assignment form data",
		"assignment_create_with_file",
		map[string]any{
			"form_values":  c.Request.Form,
			"content_type": c.Request.Header.Get("Content-Type"),
		},
	)

	// Parse form fields
	title := c.PostForm("title")
	if title == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "title is required"})
		return
	}

	description := c.PostForm("description")
	dueDateStr := c.PostForm("due_date")
	maxPointsStr := c.PostForm("max_points")
	assignmentGroupIdStr := c.PostForm("assignment_group_id")
	assignmentType := c.PostForm("type")

	// Log the parsed values for debugging
	h.RabbitLogPublisher.PublishLog(
		"info",
		"Parsed assignment form data",
		"assignment_create_with_file",
		map[string]any{
			"title":               title,
			"description":         description,
			"due_date":            dueDateStr,
			"max_points":          maxPointsStr,
			"assignment_group_id": assignmentGroupIdStr,
			"type":                assignmentType,
		},
	)

	if assignmentType == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "type is required"})
		return
	}

	// Clean up form values by trimming whitespace
	assignmentType = strings.TrimSpace(assignmentType)
	title = strings.TrimSpace(title)
	description = strings.TrimSpace(description)
	dueDateStr = strings.TrimSpace(dueDateStr)
	maxPointsStr = strings.TrimSpace(maxPointsStr)
	assignmentGroupIdStr = strings.TrimSpace(assignmentGroupIdStr)

	// Log the cleaned values for debugging
	h.RabbitLogPublisher.PublishLog(
		"info",
		"Cleaned assignment form data",
		"assignment_create_with_file",
		map[string]any{
			"title":               title,
			"description":         description,
			"due_date":            dueDateStr,
			"max_points":          maxPointsStr,
			"assignment_group_id": assignmentGroupIdStr,
			"type":                assignmentType,
		},
	)

	// Validate based on assignment type
	if assignmentType == "task" {
		// For task type, due_date and max_points are required
		if dueDateStr == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "due_date is required for task type"})
			return
		}

		if maxPointsStr == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "max_points must be provided for task type"})
			return
		}
	} else if assignmentType != "info" {
		// Log the invalid type for debugging
		h.RabbitLogPublisher.PublishLog(
			"error",
			"Invalid assignment type",
			"assignment_create_with_file",
			map[string]any{
				"type":        assignmentType,
				"valid_types": []string{"task", "info"},
			},
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "type must be either 'task' or 'info'"})
		return
	}

	// Parse max points
	var maxPoints int32 = 0
	if maxPointsStr != "" {
		maxPointsInt, err := strconv.ParseInt(maxPointsStr, 10, 32)
		if err != nil || maxPointsInt <= 0 {
			c.JSON(http.StatusBadRequest, gin.H{"error": "max_points must be a positive number"})
			return
		}
		maxPoints = int32(maxPointsInt)
	}

	// Parse assignment group ID
	var assignmentGroupId int64 = 0
	if assignmentGroupIdStr != "" {
		var err error
		assignmentGroupId, err = strconv.ParseInt(assignmentGroupIdStr, 10, 64)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid assignment_group_id"})
			return
		}
	}

	// Parse date if provided
	var dt time.Time
	if dueDateStr != "" {
		parsedDt, parseErr := time.Parse(time.RFC3339, dueDateStr)
		if parseErr != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid due_date format, use RFC3339"})
			return
		}
		dt = parsedDt
	} else {
		// For info type without due_date, set a far future date
		dt = time.Now().AddDate(10, 0, 0) // 10 years in the future
	}

	// Create the assignment
	resp, err := h.AssignmentService.CreateAssignment(
		c.Request.Context(),
		weekID,
		title,
		description,
		timestamppb.New(dt),
		maxPoints,
		assignmentGroupId,
		assignmentType,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Check if there's a file to upload
	file, header, err := c.Request.FormFile("file")
	if err == nil {
		// We have a file to upload
		defer file.Close()

		// Check file size
		fileSize := header.Size
		if fileSize > 10*1024*1024 { // 10MB limit
			c.JSON(http.StatusBadRequest, gin.H{"error": "file size exceeds 10MB limit"})
			return
		}

		// Get content type
		contentType := header.Header.Get("Content-Type")
		if contentType == "" {
			contentType = "application/octet-stream"
		}

		// Read file content
		fileData := make([]byte, fileSize)
		_, err := file.Read(fileData)
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error reading assignment file: %v", err),
				"assignment_create_with_file",
				map[string]any{"title": title, "error": err.Error()},
			)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to read file"})
			return
		}

		// Process filename
		originalFilename := header.Filename
		safeFilename := strings.ReplaceAll(originalFilename, " ", "_")
		objectName := fmt.Sprintf("%d_%s", time.Now().UnixNano(), safeFilename)

		// Upload file to storage
		_, err = h.StorageService.UploadFile(
			c.Request.Context(),
			"assignment-files",
			objectName,
			contentType,
			fileData,
		)
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error uploading assignment file: %v", err),
				"assignment_create_with_file",
				map[string]any{"title": title, "error": err.Error()},
			)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to upload file"})
			return
		}

		// Create attachment for the assignment
		// Always use just the bucket and filename, not the full URL with host
		fileUrl := fmt.Sprintf("assignment-files/%s", objectName)

		attachResp, err := h.AssignmentService.CreateAssignmentAttachment(
			c.Request.Context(),
			resp.Id,
			fileUrl,
		)
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error creating assignment attachment: %v", err),
				"assignment_create_with_file",
				map[string]any{"assignment_id": resp.Id, "error": err.Error()},
			)
			// Don't return an error here, as the assignment was created successfully
			// Just log the error and continue
		}

		// Return the assignment with the attachment
		c.JSON(http.StatusCreated, gin.H{
			"assignment": resp,
			"attachment": attachResp,
		})
		return
	}

	// If we get here, there was no file or there was an error getting the file
	// Just return the assignment
	c.JSON(http.StatusCreated, resp)
}

// ListAssignmentsForWeekHandler возвращает все задачи недели
// GET /weeks/:weekId/assignments
func (h *AssignmentHandler) ListAssignmentsForWeekHandler(c *gin.Context) {
	weekID, err := strconv.ParseInt(c.Param("weekId"), 10, 64)
	if err != nil || weekID <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid weekId"})
		return
	}

	list, err := h.AssignmentService.ListAssignmentsForWeek(c.Request.Context(), weekID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, list)
}

// GetAssignmentByIDHandler возвращает задачу по ID с вложениями
// GET /assignments/:id
func (h *AssignmentHandler) GetAssignmentByIDHandler(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil || id <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid assignment ID"})
		return
	}

	// Get the assignment details
	assignment, err := h.AssignmentService.GetAssignmentByID(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Get the assignment attachments
	attachments, err := h.AssignmentService.GetAssignmentAttachmentsByAssignmentID(c.Request.Context(), id)
	if err != nil {
		// Log the error but continue
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error fetching assignment attachments: %v", err),
			"get_assignment_by_id",
			map[string]any{
				"assignment_id": id,
				"error":         err.Error(),
			},
		)
		// Don't return, just continue with empty attachments
		attachments = []*assignmentpb.AssignmentAttachmentResponse{}
	}

	// Return the assignment with attachments
	c.JSON(http.StatusOK, gin.H{
		"assignment":  assignment,
		"attachments": attachments,
	})
}

// UpdateAssignmentByIDHandler обновляет задачу
// PUT /assignments/:id
func (h *AssignmentHandler) UpdateAssignmentByIDHandler(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil || id <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid assignment ID"})
		return
	}

	var input struct {
		WeekId            int64  `json:"week_id" binding:"required"`
		Title             string `json:"title" binding:"required"`
		Description       string `json:"description"`
		DueDate           string `json:"due_date"`   // Required only for task type
		MaxPoints         int32  `json:"max_points"` // Required only for task type
		AssignmentGroupId int64  `json:"assignment_group_id"`
		Type              string `json:"type" binding:"required"`
	}
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate based on assignment type
	if input.Type == "task" {
		// For task type, due_date and max_points are required
		if input.DueDate == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "due_date is required for task type"})
			return
		}
		if input.MaxPoints <= 0 {
			c.JSON(http.StatusBadRequest, gin.H{"error": "max_points must be > 0 for task type"})
			return
		}
	} else if input.Type != "info" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "type must be either 'task' or 'info'"})
		return
	}

	// Parse date if provided
	var dt time.Time
	if input.DueDate != "" {
		parsedDt, parseErr := time.Parse(time.RFC3339, input.DueDate)
		if parseErr != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid due_date format, use RFC3339"})
			return
		}
		dt = parsedDt
	} else {
		// For info type without due_date, set a far future date
		dt = time.Now().AddDate(10, 0, 0) // 10 years in the future
	}

	resp, err := h.AssignmentService.UpdateAssignmentByID(
		c.Request.Context(),
		id,
		input.WeekId,
		input.Title,
		input.Description,
		timestamppb.New(dt),
		input.MaxPoints,
		input.AssignmentGroupId,
		input.Type,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, resp)
}

// DeleteAssignmentByIDHandler удаляет задачу
// DELETE /assignments/:id
func (h *AssignmentHandler) DeleteAssignmentByIDHandler(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil || id <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid assignment ID"})
		return
	}

	if err := h.AssignmentService.DeleteAssignmentByID(c.Request.Context(), id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.Status(http.StatusNoContent)
}

func (h *AssignmentHandler) ListAssignmentsWithSubmissionForThread(c *gin.Context) {
	// валидируем
	tid, err := strconv.ParseInt(c.Query("thread_id"), 10, 64)
	if err != nil || tid <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid thread_id"})
		return
	}
	uid, err := strconv.ParseInt(c.Query("user_id"), 10, 64)
	if err != nil || uid <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid user_id"})
		return
	}

	items, err := h.AssignmentService.ListAssignmentsWithSubmissionForThread(c.Request.Context(), tid, uid)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"thread_id":   tid,
		"user_id":     uid,
		"assignments": items,
	})
}

// GetAssignmentDetailsForStudent returns comprehensive assignment details for a student
// GET /assignments/:id/student/:student_id
func (h *AssignmentHandler) GetAssignmentDetailsForStudent(c *gin.Context) {
	// Parse assignment ID
	assignmentID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil || assignmentID <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid assignment ID"})
		return
	}

	// Parse student ID
	studentID, err := strconv.ParseInt(c.Param("user_id"), 10, 64)
	if err != nil || studentID <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid student ID"})
		return
	}

	// Get comprehensive assignment details
	details, err := h.AssignmentService.GetAssignmentDetailsForStudent(c.Request.Context(), assignmentID, studentID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, details)
}

// GetThreadGradebookHandler returns assignment and student grade information for a thread
// GET /assignments/thread/:threadId/gradebook
func (h *AssignmentHandler) GetThreadGradebookHandler(c *gin.Context) {
	// Parse thread ID
	threadID, err := strconv.ParseInt(c.Param("threadId"), 10, 64)
	if err != nil || threadID <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid thread ID"})
		return
	}

	// Get gradebook data from assignment service
	gradebook, err := h.AssignmentService.GetThreadGradebook(c.Request.Context(), threadID)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error fetching thread gradebook: %v", err),
			"get_thread_gradebook",
			map[string]any{
				"thread_id": threadID,
				"error":     err.Error(),
			},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Transform protobuf response to match the requested format
	assignments := make([]map[string]interface{}, 0, len(gradebook.Assignments))
	for _, assignment := range gradebook.Assignments {
		assignments = append(assignments, map[string]interface{}{
			"id":         assignment.Id,
			"title":      assignment.Title,
			"max_points": assignment.MaxPoints,
		})
	}

	students := make([]map[string]interface{}, 0, len(gradebook.Students))
	for _, student := range gradebook.Students {
		grades := make([]map[string]interface{}, 0, len(student.Grades))
		for _, grade := range student.Grades {
			gradeMap := map[string]interface{}{
				"assignment_id": grade.AssignmentId,
			}
			if grade.HasScore {
				gradeMap["score"] = grade.Score
			}
			grades = append(grades, gradeMap)
		}

		studentMap := map[string]interface{}{
			"id":      student.Id,
			"name":    student.Name,
			"surname": student.Surname,
			"grades":  grades,
		}

		if student.HasFinalGrade {
			studentMap["final_grade"] = student.FinalGrade
		} else {
			studentMap["final_grade"] = nil
		}

		students = append(students, studentMap)
	}

	response := map[string]interface{}{
		"assignments": assignments,
		"students":    students,
	}

	c.JSON(http.StatusOK, response)
}
