// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.28.2
// source: proto/notification.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Enums
type NotificationType int32

const (
	NotificationType_INFO         NotificationType = 0
	NotificationType_WARNING      NotificationType = 1
	NotificationType_SUCCESS      NotificationType = 2
	NotificationType_ERROR        NotificationType = 3
	NotificationType_ANNOUNCEMENT NotificationType = 4
)

// Enum value maps for NotificationType.
var (
	NotificationType_name = map[int32]string{
		0: "INFO",
		1: "WARNING",
		2: "SUCCESS",
		3: "ERROR",
		4: "ANNOUNCEMENT",
	}
	NotificationType_value = map[string]int32{
		"INFO":         0,
		"WARNING":      1,
		"SUCCESS":      2,
		"ERROR":        3,
		"ANNOUNCEMENT": 4,
	}
)

func (x NotificationType) Enum() *NotificationType {
	p := new(NotificationType)
	*p = x
	return p
}

func (x NotificationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NotificationType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_notification_proto_enumTypes[0].Descriptor()
}

func (NotificationType) Type() protoreflect.EnumType {
	return &file_proto_notification_proto_enumTypes[0]
}

func (x NotificationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NotificationType.Descriptor instead.
func (NotificationType) EnumDescriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{0}
}

type NotificationPriority int32

const (
	NotificationPriority_LOW    NotificationPriority = 0
	NotificationPriority_NORMAL NotificationPriority = 1
	NotificationPriority_HIGH   NotificationPriority = 2
	NotificationPriority_URGENT NotificationPriority = 3
)

// Enum value maps for NotificationPriority.
var (
	NotificationPriority_name = map[int32]string{
		0: "LOW",
		1: "NORMAL",
		2: "HIGH",
		3: "URGENT",
	}
	NotificationPriority_value = map[string]int32{
		"LOW":    0,
		"NORMAL": 1,
		"HIGH":   2,
		"URGENT": 3,
	}
)

func (x NotificationPriority) Enum() *NotificationPriority {
	p := new(NotificationPriority)
	*p = x
	return p
}

func (x NotificationPriority) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NotificationPriority) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_notification_proto_enumTypes[1].Descriptor()
}

func (NotificationPriority) Type() protoreflect.EnumType {
	return &file_proto_notification_proto_enumTypes[1]
}

func (x NotificationPriority) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NotificationPriority.Descriptor instead.
func (NotificationPriority) EnumDescriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{1}
}

type TargetType int32

const (
	TargetType_ALL    TargetType = 0
	TargetType_ROLE   TargetType = 1
	TargetType_USER   TargetType = 2
	TargetType_DEGREE TargetType = 3
	TargetType_COURSE TargetType = 4
	TargetType_THREAD TargetType = 5
)

// Enum value maps for TargetType.
var (
	TargetType_name = map[int32]string{
		0: "ALL",
		1: "ROLE",
		2: "USER",
		3: "DEGREE",
		4: "COURSE",
		5: "THREAD",
	}
	TargetType_value = map[string]int32{
		"ALL":    0,
		"ROLE":   1,
		"USER":   2,
		"DEGREE": 3,
		"COURSE": 4,
		"THREAD": 5,
	}
)

func (x TargetType) Enum() *TargetType {
	p := new(TargetType)
	*p = x
	return p
}

func (x TargetType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TargetType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_notification_proto_enumTypes[2].Descriptor()
}

func (TargetType) Type() protoreflect.EnumType {
	return &file_proto_notification_proto_enumTypes[2]
}

func (x TargetType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TargetType.Descriptor instead.
func (TargetType) EnumDescriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{2}
}

// Messages
type Notification struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Title         string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Message       string                 `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	Type          NotificationType       `protobuf:"varint,4,opt,name=type,proto3,enum=notification.NotificationType" json:"type,omitempty"`
	Priority      NotificationPriority   `protobuf:"varint,5,opt,name=priority,proto3,enum=notification.NotificationPriority" json:"priority,omitempty"`
	TargetType    TargetType             `protobuf:"varint,6,opt,name=target_type,json=targetType,proto3,enum=notification.TargetType" json:"target_type,omitempty"`
	TargetValue   string                 `protobuf:"bytes,7,opt,name=target_value,json=targetValue,proto3" json:"target_value,omitempty"`
	SenderId      int64                  `protobuf:"varint,8,opt,name=sender_id,json=senderId,proto3" json:"sender_id,omitempty"`
	SendEmail     bool                   `protobuf:"varint,9,opt,name=send_email,json=sendEmail,proto3" json:"send_email,omitempty"`
	EmailSubject  string                 `protobuf:"bytes,10,opt,name=email_subject,json=emailSubject,proto3" json:"email_subject,omitempty"`
	EmailTemplate string                 `protobuf:"bytes,11,opt,name=email_template,json=emailTemplate,proto3" json:"email_template,omitempty"`
	ScheduledAt   *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=scheduled_at,json=scheduledAt,proto3" json:"scheduled_at,omitempty"`
	SentAt        *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=sent_at,json=sentAt,proto3" json:"sent_at,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Notification) Reset() {
	*x = Notification{}
	mi := &file_proto_notification_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Notification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Notification) ProtoMessage() {}

func (x *Notification) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Notification.ProtoReflect.Descriptor instead.
func (*Notification) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{0}
}

func (x *Notification) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Notification) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Notification) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *Notification) GetType() NotificationType {
	if x != nil {
		return x.Type
	}
	return NotificationType_INFO
}

func (x *Notification) GetPriority() NotificationPriority {
	if x != nil {
		return x.Priority
	}
	return NotificationPriority_LOW
}

func (x *Notification) GetTargetType() TargetType {
	if x != nil {
		return x.TargetType
	}
	return TargetType_ALL
}

func (x *Notification) GetTargetValue() string {
	if x != nil {
		return x.TargetValue
	}
	return ""
}

func (x *Notification) GetSenderId() int64 {
	if x != nil {
		return x.SenderId
	}
	return 0
}

func (x *Notification) GetSendEmail() bool {
	if x != nil {
		return x.SendEmail
	}
	return false
}

func (x *Notification) GetEmailSubject() string {
	if x != nil {
		return x.EmailSubject
	}
	return ""
}

func (x *Notification) GetEmailTemplate() string {
	if x != nil {
		return x.EmailTemplate
	}
	return ""
}

func (x *Notification) GetScheduledAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ScheduledAt
	}
	return nil
}

func (x *Notification) GetSentAt() *timestamppb.Timestamp {
	if x != nil {
		return x.SentAt
	}
	return nil
}

func (x *Notification) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Notification) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type NotificationRecipient struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	NotificationId int64                  `protobuf:"varint,2,opt,name=notification_id,json=notificationId,proto3" json:"notification_id,omitempty"`
	UserId         int64                  `protobuf:"varint,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	IsRead         bool                   `protobuf:"varint,4,opt,name=is_read,json=isRead,proto3" json:"is_read,omitempty"`
	ReadAt         *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=read_at,json=readAt,proto3" json:"read_at,omitempty"`
	EmailSent      bool                   `protobuf:"varint,6,opt,name=email_sent,json=emailSent,proto3" json:"email_sent,omitempty"`
	EmailSentAt    *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=email_sent_at,json=emailSentAt,proto3" json:"email_sent_at,omitempty"`
	CreatedAt      *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt      *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *NotificationRecipient) Reset() {
	*x = NotificationRecipient{}
	mi := &file_proto_notification_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NotificationRecipient) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotificationRecipient) ProtoMessage() {}

func (x *NotificationRecipient) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotificationRecipient.ProtoReflect.Descriptor instead.
func (*NotificationRecipient) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{1}
}

func (x *NotificationRecipient) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *NotificationRecipient) GetNotificationId() int64 {
	if x != nil {
		return x.NotificationId
	}
	return 0
}

func (x *NotificationRecipient) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *NotificationRecipient) GetIsRead() bool {
	if x != nil {
		return x.IsRead
	}
	return false
}

func (x *NotificationRecipient) GetReadAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ReadAt
	}
	return nil
}

func (x *NotificationRecipient) GetEmailSent() bool {
	if x != nil {
		return x.EmailSent
	}
	return false
}

func (x *NotificationRecipient) GetEmailSentAt() *timestamppb.Timestamp {
	if x != nil {
		return x.EmailSentAt
	}
	return nil
}

func (x *NotificationRecipient) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *NotificationRecipient) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type UserNotification struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Notification  *Notification          `protobuf:"bytes,1,opt,name=notification,proto3" json:"notification,omitempty"`
	Recipient     *NotificationRecipient `protobuf:"bytes,2,opt,name=recipient,proto3" json:"recipient,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserNotification) Reset() {
	*x = UserNotification{}
	mi := &file_proto_notification_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserNotification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserNotification) ProtoMessage() {}

func (x *UserNotification) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserNotification.ProtoReflect.Descriptor instead.
func (*UserNotification) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{2}
}

func (x *UserNotification) GetNotification() *Notification {
	if x != nil {
		return x.Notification
	}
	return nil
}

func (x *UserNotification) GetRecipient() *NotificationRecipient {
	if x != nil {
		return x.Recipient
	}
	return nil
}

type EmailTemplate struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Subject       string                 `protobuf:"bytes,3,opt,name=subject,proto3" json:"subject,omitempty"`
	HtmlContent   string                 `protobuf:"bytes,4,opt,name=html_content,json=htmlContent,proto3" json:"html_content,omitempty"`
	TextContent   string                 `protobuf:"bytes,5,opt,name=text_content,json=textContent,proto3" json:"text_content,omitempty"`
	Variables     string                 `protobuf:"bytes,6,opt,name=variables,proto3" json:"variables,omitempty"` // JSON string
	IsActive      bool                   `protobuf:"varint,7,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EmailTemplate) Reset() {
	*x = EmailTemplate{}
	mi := &file_proto_notification_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EmailTemplate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmailTemplate) ProtoMessage() {}

func (x *EmailTemplate) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmailTemplate.ProtoReflect.Descriptor instead.
func (*EmailTemplate) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{3}
}

func (x *EmailTemplate) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *EmailTemplate) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *EmailTemplate) GetSubject() string {
	if x != nil {
		return x.Subject
	}
	return ""
}

func (x *EmailTemplate) GetHtmlContent() string {
	if x != nil {
		return x.HtmlContent
	}
	return ""
}

func (x *EmailTemplate) GetTextContent() string {
	if x != nil {
		return x.TextContent
	}
	return ""
}

func (x *EmailTemplate) GetVariables() string {
	if x != nil {
		return x.Variables
	}
	return ""
}

func (x *EmailTemplate) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *EmailTemplate) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *EmailTemplate) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// Request/Response messages
type CreateNotificationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Title         string                 `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Type          NotificationType       `protobuf:"varint,3,opt,name=type,proto3,enum=notification.NotificationType" json:"type,omitempty"`
	Priority      NotificationPriority   `protobuf:"varint,4,opt,name=priority,proto3,enum=notification.NotificationPriority" json:"priority,omitempty"`
	TargetType    TargetType             `protobuf:"varint,5,opt,name=target_type,json=targetType,proto3,enum=notification.TargetType" json:"target_type,omitempty"`
	TargetValue   string                 `protobuf:"bytes,6,opt,name=target_value,json=targetValue,proto3" json:"target_value,omitempty"`
	SenderId      int64                  `protobuf:"varint,7,opt,name=sender_id,json=senderId,proto3" json:"sender_id,omitempty"`
	SendEmail     bool                   `protobuf:"varint,8,opt,name=send_email,json=sendEmail,proto3" json:"send_email,omitempty"`
	EmailSubject  string                 `protobuf:"bytes,9,opt,name=email_subject,json=emailSubject,proto3" json:"email_subject,omitempty"`
	EmailTemplate string                 `protobuf:"bytes,10,opt,name=email_template,json=emailTemplate,proto3" json:"email_template,omitempty"`
	ScheduledAt   *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=scheduled_at,json=scheduledAt,proto3" json:"scheduled_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateNotificationRequest) Reset() {
	*x = CreateNotificationRequest{}
	mi := &file_proto_notification_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateNotificationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateNotificationRequest) ProtoMessage() {}

func (x *CreateNotificationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateNotificationRequest.ProtoReflect.Descriptor instead.
func (*CreateNotificationRequest) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{4}
}

func (x *CreateNotificationRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *CreateNotificationRequest) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CreateNotificationRequest) GetType() NotificationType {
	if x != nil {
		return x.Type
	}
	return NotificationType_INFO
}

func (x *CreateNotificationRequest) GetPriority() NotificationPriority {
	if x != nil {
		return x.Priority
	}
	return NotificationPriority_LOW
}

func (x *CreateNotificationRequest) GetTargetType() TargetType {
	if x != nil {
		return x.TargetType
	}
	return TargetType_ALL
}

func (x *CreateNotificationRequest) GetTargetValue() string {
	if x != nil {
		return x.TargetValue
	}
	return ""
}

func (x *CreateNotificationRequest) GetSenderId() int64 {
	if x != nil {
		return x.SenderId
	}
	return 0
}

func (x *CreateNotificationRequest) GetSendEmail() bool {
	if x != nil {
		return x.SendEmail
	}
	return false
}

func (x *CreateNotificationRequest) GetEmailSubject() string {
	if x != nil {
		return x.EmailSubject
	}
	return ""
}

func (x *CreateNotificationRequest) GetEmailTemplate() string {
	if x != nil {
		return x.EmailTemplate
	}
	return ""
}

func (x *CreateNotificationRequest) GetScheduledAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ScheduledAt
	}
	return nil
}

type NotificationResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Notification  *Notification          `protobuf:"bytes,1,opt,name=notification,proto3" json:"notification,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Success       bool                   `protobuf:"varint,3,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NotificationResponse) Reset() {
	*x = NotificationResponse{}
	mi := &file_proto_notification_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NotificationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotificationResponse) ProtoMessage() {}

func (x *NotificationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotificationResponse.ProtoReflect.Descriptor instead.
func (*NotificationResponse) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{5}
}

func (x *NotificationResponse) GetNotification() *Notification {
	if x != nil {
		return x.Notification
	}
	return nil
}

func (x *NotificationResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *NotificationResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type GetUserNotificationsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Page          int32                  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	Limit         int32                  `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	UnreadOnly    bool                   `protobuf:"varint,4,opt,name=unread_only,json=unreadOnly,proto3" json:"unread_only,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserNotificationsRequest) Reset() {
	*x = GetUserNotificationsRequest{}
	mi := &file_proto_notification_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserNotificationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserNotificationsRequest) ProtoMessage() {}

func (x *GetUserNotificationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserNotificationsRequest.ProtoReflect.Descriptor instead.
func (*GetUserNotificationsRequest) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{6}
}

func (x *GetUserNotificationsRequest) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *GetUserNotificationsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetUserNotificationsRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *GetUserNotificationsRequest) GetUnreadOnly() bool {
	if x != nil {
		return x.UnreadOnly
	}
	return false
}

type GetUserNotificationsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Notifications []*UserNotification    `protobuf:"bytes,1,rep,name=notifications,proto3" json:"notifications,omitempty"`
	TotalCount    int32                  `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	UnreadCount   int32                  `protobuf:"varint,3,opt,name=unread_count,json=unreadCount,proto3" json:"unread_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserNotificationsResponse) Reset() {
	*x = GetUserNotificationsResponse{}
	mi := &file_proto_notification_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserNotificationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserNotificationsResponse) ProtoMessage() {}

func (x *GetUserNotificationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserNotificationsResponse.ProtoReflect.Descriptor instead.
func (*GetUserNotificationsResponse) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{7}
}

func (x *GetUserNotificationsResponse) GetNotifications() []*UserNotification {
	if x != nil {
		return x.Notifications
	}
	return nil
}

func (x *GetUserNotificationsResponse) GetTotalCount() int32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *GetUserNotificationsResponse) GetUnreadCount() int32 {
	if x != nil {
		return x.UnreadCount
	}
	return 0
}

type GetAllNotificationsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Limit         int32                  `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	TargetType    TargetType             `protobuf:"varint,3,opt,name=target_type,json=targetType,proto3,enum=notification.TargetType" json:"target_type,omitempty"`
	TargetValue   string                 `protobuf:"bytes,4,opt,name=target_value,json=targetValue,proto3" json:"target_value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllNotificationsRequest) Reset() {
	*x = GetAllNotificationsRequest{}
	mi := &file_proto_notification_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllNotificationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllNotificationsRequest) ProtoMessage() {}

func (x *GetAllNotificationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllNotificationsRequest.ProtoReflect.Descriptor instead.
func (*GetAllNotificationsRequest) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{8}
}

func (x *GetAllNotificationsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetAllNotificationsRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *GetAllNotificationsRequest) GetTargetType() TargetType {
	if x != nil {
		return x.TargetType
	}
	return TargetType_ALL
}

func (x *GetAllNotificationsRequest) GetTargetValue() string {
	if x != nil {
		return x.TargetValue
	}
	return ""
}

type GetAllNotificationsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Notifications []*Notification        `protobuf:"bytes,1,rep,name=notifications,proto3" json:"notifications,omitempty"`
	TotalCount    int32                  `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllNotificationsResponse) Reset() {
	*x = GetAllNotificationsResponse{}
	mi := &file_proto_notification_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllNotificationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllNotificationsResponse) ProtoMessage() {}

func (x *GetAllNotificationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllNotificationsResponse.ProtoReflect.Descriptor instead.
func (*GetAllNotificationsResponse) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{9}
}

func (x *GetAllNotificationsResponse) GetNotifications() []*Notification {
	if x != nil {
		return x.Notifications
	}
	return nil
}

func (x *GetAllNotificationsResponse) GetTotalCount() int32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type MarkAsReadRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	NotificationId int64                  `protobuf:"varint,1,opt,name=notification_id,json=notificationId,proto3" json:"notification_id,omitempty"`
	UserId         int64                  `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *MarkAsReadRequest) Reset() {
	*x = MarkAsReadRequest{}
	mi := &file_proto_notification_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MarkAsReadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkAsReadRequest) ProtoMessage() {}

func (x *MarkAsReadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkAsReadRequest.ProtoReflect.Descriptor instead.
func (*MarkAsReadRequest) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{10}
}

func (x *MarkAsReadRequest) GetNotificationId() int64 {
	if x != nil {
		return x.NotificationId
	}
	return 0
}

func (x *MarkAsReadRequest) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type MarkAsReadResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MarkAsReadResponse) Reset() {
	*x = MarkAsReadResponse{}
	mi := &file_proto_notification_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MarkAsReadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkAsReadResponse) ProtoMessage() {}

func (x *MarkAsReadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkAsReadResponse.ProtoReflect.Descriptor instead.
func (*MarkAsReadResponse) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{11}
}

func (x *MarkAsReadResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *MarkAsReadResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type DeleteNotificationRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	NotificationId int64                  `protobuf:"varint,1,opt,name=notification_id,json=notificationId,proto3" json:"notification_id,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *DeleteNotificationRequest) Reset() {
	*x = DeleteNotificationRequest{}
	mi := &file_proto_notification_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteNotificationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteNotificationRequest) ProtoMessage() {}

func (x *DeleteNotificationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteNotificationRequest.ProtoReflect.Descriptor instead.
func (*DeleteNotificationRequest) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{12}
}

func (x *DeleteNotificationRequest) GetNotificationId() int64 {
	if x != nil {
		return x.NotificationId
	}
	return 0
}

type DeleteNotificationResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteNotificationResponse) Reset() {
	*x = DeleteNotificationResponse{}
	mi := &file_proto_notification_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteNotificationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteNotificationResponse) ProtoMessage() {}

func (x *DeleteNotificationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteNotificationResponse.ProtoReflect.Descriptor instead.
func (*DeleteNotificationResponse) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{13}
}

func (x *DeleteNotificationResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *DeleteNotificationResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type SendScheduledNotificationsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendScheduledNotificationsRequest) Reset() {
	*x = SendScheduledNotificationsRequest{}
	mi := &file_proto_notification_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendScheduledNotificationsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendScheduledNotificationsRequest) ProtoMessage() {}

func (x *SendScheduledNotificationsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendScheduledNotificationsRequest.ProtoReflect.Descriptor instead.
func (*SendScheduledNotificationsRequest) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{14}
}

type SendScheduledNotificationsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SentCount     int32                  `protobuf:"varint,1,opt,name=sent_count,json=sentCount,proto3" json:"sent_count,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendScheduledNotificationsResponse) Reset() {
	*x = SendScheduledNotificationsResponse{}
	mi := &file_proto_notification_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendScheduledNotificationsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendScheduledNotificationsResponse) ProtoMessage() {}

func (x *SendScheduledNotificationsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendScheduledNotificationsResponse.ProtoReflect.Descriptor instead.
func (*SendScheduledNotificationsResponse) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{15}
}

func (x *SendScheduledNotificationsResponse) GetSentCount() int32 {
	if x != nil {
		return x.SentCount
	}
	return 0
}

func (x *SendScheduledNotificationsResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type GetNotificationStatsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"` // If 0, get global stats (admin only)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetNotificationStatsRequest) Reset() {
	*x = GetNotificationStatsRequest{}
	mi := &file_proto_notification_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetNotificationStatsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNotificationStatsRequest) ProtoMessage() {}

func (x *GetNotificationStatsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNotificationStatsRequest.ProtoReflect.Descriptor instead.
func (*GetNotificationStatsRequest) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{16}
}

func (x *GetNotificationStatsRequest) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type GetNotificationStatsResponse struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	TotalNotifications  int32                  `protobuf:"varint,1,opt,name=total_notifications,json=totalNotifications,proto3" json:"total_notifications,omitempty"`
	UnreadNotifications int32                  `protobuf:"varint,2,opt,name=unread_notifications,json=unreadNotifications,proto3" json:"unread_notifications,omitempty"`
	ReadNotifications   int32                  `protobuf:"varint,3,opt,name=read_notifications,json=readNotifications,proto3" json:"read_notifications,omitempty"`
	EmailNotifications  int32                  `protobuf:"varint,4,opt,name=email_notifications,json=emailNotifications,proto3" json:"email_notifications,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *GetNotificationStatsResponse) Reset() {
	*x = GetNotificationStatsResponse{}
	mi := &file_proto_notification_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetNotificationStatsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNotificationStatsResponse) ProtoMessage() {}

func (x *GetNotificationStatsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNotificationStatsResponse.ProtoReflect.Descriptor instead.
func (*GetNotificationStatsResponse) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{17}
}

func (x *GetNotificationStatsResponse) GetTotalNotifications() int32 {
	if x != nil {
		return x.TotalNotifications
	}
	return 0
}

func (x *GetNotificationStatsResponse) GetUnreadNotifications() int32 {
	if x != nil {
		return x.UnreadNotifications
	}
	return 0
}

func (x *GetNotificationStatsResponse) GetReadNotifications() int32 {
	if x != nil {
		return x.ReadNotifications
	}
	return 0
}

func (x *GetNotificationStatsResponse) GetEmailNotifications() int32 {
	if x != nil {
		return x.EmailNotifications
	}
	return 0
}

type GetEmailTemplatesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ActiveOnly    bool                   `protobuf:"varint,1,opt,name=active_only,json=activeOnly,proto3" json:"active_only,omitempty"` // If true, return only active templates
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetEmailTemplatesRequest) Reset() {
	*x = GetEmailTemplatesRequest{}
	mi := &file_proto_notification_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEmailTemplatesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEmailTemplatesRequest) ProtoMessage() {}

func (x *GetEmailTemplatesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEmailTemplatesRequest.ProtoReflect.Descriptor instead.
func (*GetEmailTemplatesRequest) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{18}
}

func (x *GetEmailTemplatesRequest) GetActiveOnly() bool {
	if x != nil {
		return x.ActiveOnly
	}
	return false
}

type GetEmailTemplatesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Templates     []*EmailTemplate       `protobuf:"bytes,1,rep,name=templates,proto3" json:"templates,omitempty"`
	TotalCount    int32                  `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetEmailTemplatesResponse) Reset() {
	*x = GetEmailTemplatesResponse{}
	mi := &file_proto_notification_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEmailTemplatesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEmailTemplatesResponse) ProtoMessage() {}

func (x *GetEmailTemplatesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_notification_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEmailTemplatesResponse.ProtoReflect.Descriptor instead.
func (*GetEmailTemplatesResponse) Descriptor() ([]byte, []int) {
	return file_proto_notification_proto_rawDescGZIP(), []int{19}
}

func (x *GetEmailTemplatesResponse) GetTemplates() []*EmailTemplate {
	if x != nil {
		return x.Templates
	}
	return nil
}

func (x *GetEmailTemplatesResponse) GetTotalCount() int32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

var File_proto_notification_proto protoreflect.FileDescriptor

const file_proto_notification_proto_rawDesc = "" +
	"\n" +
	"\x18proto/notification.proto\x12\fnotification\x1a\x1fgoogle/protobuf/timestamp.proto\"\x92\x05\n" +
	"\fNotification\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x14\n" +
	"\x05title\x18\x02 \x01(\tR\x05title\x12\x18\n" +
	"\amessage\x18\x03 \x01(\tR\amessage\x122\n" +
	"\x04type\x18\x04 \x01(\x0e2\x1e.notification.NotificationTypeR\x04type\x12>\n" +
	"\bpriority\x18\x05 \x01(\x0e2\".notification.NotificationPriorityR\bpriority\x129\n" +
	"\vtarget_type\x18\x06 \x01(\x0e2\x18.notification.TargetTypeR\n" +
	"targetType\x12!\n" +
	"\ftarget_value\x18\a \x01(\tR\vtargetValue\x12\x1b\n" +
	"\tsender_id\x18\b \x01(\x03R\bsenderId\x12\x1d\n" +
	"\n" +
	"send_email\x18\t \x01(\bR\tsendEmail\x12#\n" +
	"\remail_subject\x18\n" +
	" \x01(\tR\femailSubject\x12%\n" +
	"\x0eemail_template\x18\v \x01(\tR\remailTemplate\x12=\n" +
	"\fscheduled_at\x18\f \x01(\v2\x1a.google.protobuf.TimestampR\vscheduledAt\x123\n" +
	"\asent_at\x18\r \x01(\v2\x1a.google.protobuf.TimestampR\x06sentAt\x129\n" +
	"\n" +
	"created_at\x18\x0e \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\x0f \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"\x8c\x03\n" +
	"\x15NotificationRecipient\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12'\n" +
	"\x0fnotification_id\x18\x02 \x01(\x03R\x0enotificationId\x12\x17\n" +
	"\auser_id\x18\x03 \x01(\x03R\x06userId\x12\x17\n" +
	"\ais_read\x18\x04 \x01(\bR\x06isRead\x123\n" +
	"\aread_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\x06readAt\x12\x1d\n" +
	"\n" +
	"email_sent\x18\x06 \x01(\bR\temailSent\x12>\n" +
	"\remail_sent_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\vemailSentAt\x129\n" +
	"\n" +
	"created_at\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"\x95\x01\n" +
	"\x10UserNotification\x12>\n" +
	"\fnotification\x18\x01 \x01(\v2\x1a.notification.NotificationR\fnotification\x12A\n" +
	"\trecipient\x18\x02 \x01(\v2#.notification.NotificationRecipientR\trecipient\"\xc4\x02\n" +
	"\rEmailTemplate\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x18\n" +
	"\asubject\x18\x03 \x01(\tR\asubject\x12!\n" +
	"\fhtml_content\x18\x04 \x01(\tR\vhtmlContent\x12!\n" +
	"\ftext_content\x18\x05 \x01(\tR\vtextContent\x12\x1c\n" +
	"\tvariables\x18\x06 \x01(\tR\tvariables\x12\x1b\n" +
	"\tis_active\x18\a \x01(\bR\bisActive\x129\n" +
	"\n" +
	"created_at\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"\xe4\x03\n" +
	"\x19CreateNotificationRequest\x12\x14\n" +
	"\x05title\x18\x01 \x01(\tR\x05title\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x122\n" +
	"\x04type\x18\x03 \x01(\x0e2\x1e.notification.NotificationTypeR\x04type\x12>\n" +
	"\bpriority\x18\x04 \x01(\x0e2\".notification.NotificationPriorityR\bpriority\x129\n" +
	"\vtarget_type\x18\x05 \x01(\x0e2\x18.notification.TargetTypeR\n" +
	"targetType\x12!\n" +
	"\ftarget_value\x18\x06 \x01(\tR\vtargetValue\x12\x1b\n" +
	"\tsender_id\x18\a \x01(\x03R\bsenderId\x12\x1d\n" +
	"\n" +
	"send_email\x18\b \x01(\bR\tsendEmail\x12#\n" +
	"\remail_subject\x18\t \x01(\tR\femailSubject\x12%\n" +
	"\x0eemail_template\x18\n" +
	" \x01(\tR\remailTemplate\x12=\n" +
	"\fscheduled_at\x18\v \x01(\v2\x1a.google.protobuf.TimestampR\vscheduledAt\"\x8a\x01\n" +
	"\x14NotificationResponse\x12>\n" +
	"\fnotification\x18\x01 \x01(\v2\x1a.notification.NotificationR\fnotification\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x18\n" +
	"\asuccess\x18\x03 \x01(\bR\asuccess\"\x81\x01\n" +
	"\x1bGetUserNotificationsRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x03R\x06userId\x12\x12\n" +
	"\x04page\x18\x02 \x01(\x05R\x04page\x12\x14\n" +
	"\x05limit\x18\x03 \x01(\x05R\x05limit\x12\x1f\n" +
	"\vunread_only\x18\x04 \x01(\bR\n" +
	"unreadOnly\"\xa8\x01\n" +
	"\x1cGetUserNotificationsResponse\x12D\n" +
	"\rnotifications\x18\x01 \x03(\v2\x1e.notification.UserNotificationR\rnotifications\x12\x1f\n" +
	"\vtotal_count\x18\x02 \x01(\x05R\n" +
	"totalCount\x12!\n" +
	"\funread_count\x18\x03 \x01(\x05R\vunreadCount\"\xa4\x01\n" +
	"\x1aGetAllNotificationsRequest\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x14\n" +
	"\x05limit\x18\x02 \x01(\x05R\x05limit\x129\n" +
	"\vtarget_type\x18\x03 \x01(\x0e2\x18.notification.TargetTypeR\n" +
	"targetType\x12!\n" +
	"\ftarget_value\x18\x04 \x01(\tR\vtargetValue\"\x80\x01\n" +
	"\x1bGetAllNotificationsResponse\x12@\n" +
	"\rnotifications\x18\x01 \x03(\v2\x1a.notification.NotificationR\rnotifications\x12\x1f\n" +
	"\vtotal_count\x18\x02 \x01(\x05R\n" +
	"totalCount\"U\n" +
	"\x11MarkAsReadRequest\x12'\n" +
	"\x0fnotification_id\x18\x01 \x01(\x03R\x0enotificationId\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\x03R\x06userId\"H\n" +
	"\x12MarkAsReadResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"D\n" +
	"\x19DeleteNotificationRequest\x12'\n" +
	"\x0fnotification_id\x18\x01 \x01(\x03R\x0enotificationId\"P\n" +
	"\x1aDeleteNotificationResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"#\n" +
	"!SendScheduledNotificationsRequest\"]\n" +
	"\"SendScheduledNotificationsResponse\x12\x1d\n" +
	"\n" +
	"sent_count\x18\x01 \x01(\x05R\tsentCount\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"6\n" +
	"\x1bGetNotificationStatsRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x03R\x06userId\"\xe2\x01\n" +
	"\x1cGetNotificationStatsResponse\x12/\n" +
	"\x13total_notifications\x18\x01 \x01(\x05R\x12totalNotifications\x121\n" +
	"\x14unread_notifications\x18\x02 \x01(\x05R\x13unreadNotifications\x12-\n" +
	"\x12read_notifications\x18\x03 \x01(\x05R\x11readNotifications\x12/\n" +
	"\x13email_notifications\x18\x04 \x01(\x05R\x12emailNotifications\";\n" +
	"\x18GetEmailTemplatesRequest\x12\x1f\n" +
	"\vactive_only\x18\x01 \x01(\bR\n" +
	"activeOnly\"w\n" +
	"\x19GetEmailTemplatesResponse\x129\n" +
	"\ttemplates\x18\x01 \x03(\v2\x1b.notification.EmailTemplateR\ttemplates\x12\x1f\n" +
	"\vtotal_count\x18\x02 \x01(\x05R\n" +
	"totalCount*S\n" +
	"\x10NotificationType\x12\b\n" +
	"\x04INFO\x10\x00\x12\v\n" +
	"\aWARNING\x10\x01\x12\v\n" +
	"\aSUCCESS\x10\x02\x12\t\n" +
	"\x05ERROR\x10\x03\x12\x10\n" +
	"\fANNOUNCEMENT\x10\x04*A\n" +
	"\x14NotificationPriority\x12\a\n" +
	"\x03LOW\x10\x00\x12\n" +
	"\n" +
	"\x06NORMAL\x10\x01\x12\b\n" +
	"\x04HIGH\x10\x02\x12\n" +
	"\n" +
	"\x06URGENT\x10\x03*M\n" +
	"\n" +
	"TargetType\x12\a\n" +
	"\x03ALL\x10\x00\x12\b\n" +
	"\x04ROLE\x10\x01\x12\b\n" +
	"\x04USER\x10\x02\x12\n" +
	"\n" +
	"\x06DEGREE\x10\x03\x12\n" +
	"\n" +
	"\x06COURSE\x10\x04\x12\n" +
	"\n" +
	"\x06THREAD\x10\x052\xe3\x06\n" +
	"\x13NotificationService\x12a\n" +
	"\x12CreateNotification\x12'.notification.CreateNotificationRequest\x1a\".notification.NotificationResponse\x12m\n" +
	"\x14GetUserNotifications\x12).notification.GetUserNotificationsRequest\x1a*.notification.GetUserNotificationsResponse\x12j\n" +
	"\x13GetAllNotifications\x12(.notification.GetAllNotificationsRequest\x1a).notification.GetAllNotificationsResponse\x12O\n" +
	"\n" +
	"MarkAsRead\x12\x1f.notification.MarkAsReadRequest\x1a .notification.MarkAsReadResponse\x12g\n" +
	"\x12DeleteNotification\x12'.notification.DeleteNotificationRequest\x1a(.notification.DeleteNotificationResponse\x12\x7f\n" +
	"\x1aSendScheduledNotifications\x12/.notification.SendScheduledNotificationsRequest\x1a0.notification.SendScheduledNotificationsResponse\x12m\n" +
	"\x14GetNotificationStats\x12).notification.GetNotificationStatsRequest\x1a*.notification.GetNotificationStatsResponse\x12d\n" +
	"\x11GetEmailTemplates\x12&.notification.GetEmailTemplatesRequest\x1a'.notification.GetEmailTemplatesResponseB>Z<github.com/olzzhas/edunite-server/notification_service/protob\x06proto3"

var (
	file_proto_notification_proto_rawDescOnce sync.Once
	file_proto_notification_proto_rawDescData []byte
)

func file_proto_notification_proto_rawDescGZIP() []byte {
	file_proto_notification_proto_rawDescOnce.Do(func() {
		file_proto_notification_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_notification_proto_rawDesc), len(file_proto_notification_proto_rawDesc)))
	})
	return file_proto_notification_proto_rawDescData
}

var file_proto_notification_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_proto_notification_proto_msgTypes = make([]protoimpl.MessageInfo, 20)
var file_proto_notification_proto_goTypes = []any{
	(NotificationType)(0),                      // 0: notification.NotificationType
	(NotificationPriority)(0),                  // 1: notification.NotificationPriority
	(TargetType)(0),                            // 2: notification.TargetType
	(*Notification)(nil),                       // 3: notification.Notification
	(*NotificationRecipient)(nil),              // 4: notification.NotificationRecipient
	(*UserNotification)(nil),                   // 5: notification.UserNotification
	(*EmailTemplate)(nil),                      // 6: notification.EmailTemplate
	(*CreateNotificationRequest)(nil),          // 7: notification.CreateNotificationRequest
	(*NotificationResponse)(nil),               // 8: notification.NotificationResponse
	(*GetUserNotificationsRequest)(nil),        // 9: notification.GetUserNotificationsRequest
	(*GetUserNotificationsResponse)(nil),       // 10: notification.GetUserNotificationsResponse
	(*GetAllNotificationsRequest)(nil),         // 11: notification.GetAllNotificationsRequest
	(*GetAllNotificationsResponse)(nil),        // 12: notification.GetAllNotificationsResponse
	(*MarkAsReadRequest)(nil),                  // 13: notification.MarkAsReadRequest
	(*MarkAsReadResponse)(nil),                 // 14: notification.MarkAsReadResponse
	(*DeleteNotificationRequest)(nil),          // 15: notification.DeleteNotificationRequest
	(*DeleteNotificationResponse)(nil),         // 16: notification.DeleteNotificationResponse
	(*SendScheduledNotificationsRequest)(nil),  // 17: notification.SendScheduledNotificationsRequest
	(*SendScheduledNotificationsResponse)(nil), // 18: notification.SendScheduledNotificationsResponse
	(*GetNotificationStatsRequest)(nil),        // 19: notification.GetNotificationStatsRequest
	(*GetNotificationStatsResponse)(nil),       // 20: notification.GetNotificationStatsResponse
	(*GetEmailTemplatesRequest)(nil),           // 21: notification.GetEmailTemplatesRequest
	(*GetEmailTemplatesResponse)(nil),          // 22: notification.GetEmailTemplatesResponse
	(*timestamppb.Timestamp)(nil),              // 23: google.protobuf.Timestamp
}
var file_proto_notification_proto_depIdxs = []int32{
	0,  // 0: notification.Notification.type:type_name -> notification.NotificationType
	1,  // 1: notification.Notification.priority:type_name -> notification.NotificationPriority
	2,  // 2: notification.Notification.target_type:type_name -> notification.TargetType
	23, // 3: notification.Notification.scheduled_at:type_name -> google.protobuf.Timestamp
	23, // 4: notification.Notification.sent_at:type_name -> google.protobuf.Timestamp
	23, // 5: notification.Notification.created_at:type_name -> google.protobuf.Timestamp
	23, // 6: notification.Notification.updated_at:type_name -> google.protobuf.Timestamp
	23, // 7: notification.NotificationRecipient.read_at:type_name -> google.protobuf.Timestamp
	23, // 8: notification.NotificationRecipient.email_sent_at:type_name -> google.protobuf.Timestamp
	23, // 9: notification.NotificationRecipient.created_at:type_name -> google.protobuf.Timestamp
	23, // 10: notification.NotificationRecipient.updated_at:type_name -> google.protobuf.Timestamp
	3,  // 11: notification.UserNotification.notification:type_name -> notification.Notification
	4,  // 12: notification.UserNotification.recipient:type_name -> notification.NotificationRecipient
	23, // 13: notification.EmailTemplate.created_at:type_name -> google.protobuf.Timestamp
	23, // 14: notification.EmailTemplate.updated_at:type_name -> google.protobuf.Timestamp
	0,  // 15: notification.CreateNotificationRequest.type:type_name -> notification.NotificationType
	1,  // 16: notification.CreateNotificationRequest.priority:type_name -> notification.NotificationPriority
	2,  // 17: notification.CreateNotificationRequest.target_type:type_name -> notification.TargetType
	23, // 18: notification.CreateNotificationRequest.scheduled_at:type_name -> google.protobuf.Timestamp
	3,  // 19: notification.NotificationResponse.notification:type_name -> notification.Notification
	5,  // 20: notification.GetUserNotificationsResponse.notifications:type_name -> notification.UserNotification
	2,  // 21: notification.GetAllNotificationsRequest.target_type:type_name -> notification.TargetType
	3,  // 22: notification.GetAllNotificationsResponse.notifications:type_name -> notification.Notification
	6,  // 23: notification.GetEmailTemplatesResponse.templates:type_name -> notification.EmailTemplate
	7,  // 24: notification.NotificationService.CreateNotification:input_type -> notification.CreateNotificationRequest
	9,  // 25: notification.NotificationService.GetUserNotifications:input_type -> notification.GetUserNotificationsRequest
	11, // 26: notification.NotificationService.GetAllNotifications:input_type -> notification.GetAllNotificationsRequest
	13, // 27: notification.NotificationService.MarkAsRead:input_type -> notification.MarkAsReadRequest
	15, // 28: notification.NotificationService.DeleteNotification:input_type -> notification.DeleteNotificationRequest
	17, // 29: notification.NotificationService.SendScheduledNotifications:input_type -> notification.SendScheduledNotificationsRequest
	19, // 30: notification.NotificationService.GetNotificationStats:input_type -> notification.GetNotificationStatsRequest
	21, // 31: notification.NotificationService.GetEmailTemplates:input_type -> notification.GetEmailTemplatesRequest
	8,  // 32: notification.NotificationService.CreateNotification:output_type -> notification.NotificationResponse
	10, // 33: notification.NotificationService.GetUserNotifications:output_type -> notification.GetUserNotificationsResponse
	12, // 34: notification.NotificationService.GetAllNotifications:output_type -> notification.GetAllNotificationsResponse
	14, // 35: notification.NotificationService.MarkAsRead:output_type -> notification.MarkAsReadResponse
	16, // 36: notification.NotificationService.DeleteNotification:output_type -> notification.DeleteNotificationResponse
	18, // 37: notification.NotificationService.SendScheduledNotifications:output_type -> notification.SendScheduledNotificationsResponse
	20, // 38: notification.NotificationService.GetNotificationStats:output_type -> notification.GetNotificationStatsResponse
	22, // 39: notification.NotificationService.GetEmailTemplates:output_type -> notification.GetEmailTemplatesResponse
	32, // [32:40] is the sub-list for method output_type
	24, // [24:32] is the sub-list for method input_type
	24, // [24:24] is the sub-list for extension type_name
	24, // [24:24] is the sub-list for extension extendee
	0,  // [0:24] is the sub-list for field type_name
}

func init() { file_proto_notification_proto_init() }
func file_proto_notification_proto_init() {
	if File_proto_notification_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_notification_proto_rawDesc), len(file_proto_notification_proto_rawDesc)),
			NumEnums:      3,
			NumMessages:   20,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_notification_proto_goTypes,
		DependencyIndexes: file_proto_notification_proto_depIdxs,
		EnumInfos:         file_proto_notification_proto_enumTypes,
		MessageInfos:      file_proto_notification_proto_msgTypes,
	}.Build()
	File_proto_notification_proto = out.File
	file_proto_notification_proto_goTypes = nil
	file_proto_notification_proto_depIdxs = nil
}
