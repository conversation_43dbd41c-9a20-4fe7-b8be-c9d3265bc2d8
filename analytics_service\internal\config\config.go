package config

import (
	"log"
	"os"
)

type DatabaseConfig struct {
	URL      string
	Host     string
	Port     string
	Name     string
	User     string
	Password string
	CA       string
}

type Config struct {
	Port     string
	Database DatabaseConfig
}

func LoadConfig() *Config {
	cfg := &Config{
		Port: getEnv("ANALYTICS_SERVICE_PORT", "50055"),
		Database: DatabaseConfig{
			URL:      getEnv("DATABASE_URL", "******************************************************/edunite"),
			Host:     getEnv("DB_HOST", "***************"),
			Port:     getEnv("DB_PORT", "5432"),
			Name:     getEnv("DB_NAME", "edunite"),
			User:     getEnv("DB_USER", "olzzhas"),
			Password: getEnv("DB_PASSWORD", "Olzhas123451"),
			CA:       getEnv("DB_CA", ""),
		},
	}

	log.Printf("Analytics Service Config loaded: Port=%s, DB=%s", cfg.Port, cfg.Database.Host)
	return cfg
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
