package main

import (
	"context"
	"log"
	"net"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/olzzhas/edunite-server/notification_service/internal/config"
	"github.com/olzzhas/edunite-server/notification_service/internal/database"
	"github.com/olzzhas/edunite-server/notification_service/internal/email"
	grpcserver "github.com/olzzhas/edunite-server/notification_service/internal/grpc"
	"github.com/olzzhas/edunite-server/notification_service/internal/repository"
	"github.com/olzzhas/edunite-server/notification_service/internal/service"
	pb "github.com/olzzhas/edunite-server/notification_service/proto"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"
)

func main() {
	// Load configuration
	cfg := config.LoadConfig()

	// Connect to database
	db := database.ConnectDB(cfg)
	defer db.Close()

	// Initialize repository
	repo := repository.NewNotificationRepository(db)

	// Initialize email service
	emailService := email.NewEmailService(cfg)

	// Initialize notification service
	notificationService := service.NewNotificationService(repo, emailService)

	// Create gRPC server
	grpcServer := grpc.NewServer()

	// Register notification service
	notificationServer := grpcserver.NewNotificationServer(notificationService)
	pb.RegisterNotificationServiceServer(grpcServer, notificationServer)

	// Enable reflection for debugging
	reflection.Register(grpcServer)

	// Start listening
	lis, err := net.Listen("tcp", ":"+cfg.Server.Port)
	if err != nil {
		log.Fatalf("Failed to listen on port %s: %v", cfg.Server.Port, err)
	}

	log.Printf("Notification service is running on port %s", cfg.Server.Port)

	// Start scheduled notification processor in background
	go startScheduledNotificationProcessor(notificationService)

	// Handle graceful shutdown
	go func() {
		sigChan := make(chan os.Signal, 1)
		signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
		<-sigChan

		log.Println("Shutting down notification service...")
		grpcServer.GracefulStop()
	}()

	// Start serving
	if err := grpcServer.Serve(lis); err != nil {
		log.Fatalf("Failed to serve: %v", err)
	}
}

// startScheduledNotificationProcessor runs a background process to send scheduled notifications
func startScheduledNotificationProcessor(notificationService service.NotificationService) {
	ticker := time.NewTicker(1 * time.Minute) // Check every minute
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
			sentCount, err := notificationService.SendScheduledNotifications(ctx)
			if err != nil {
				log.Printf("Error processing scheduled notifications: %v", err)
			} else if sentCount > 0 {
				log.Printf("Processed %d scheduled notifications", sentCount)
			}
			cancel()
		}
	}
}
