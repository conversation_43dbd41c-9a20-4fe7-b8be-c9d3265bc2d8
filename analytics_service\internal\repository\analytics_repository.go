package repository

import (
	"context"
	"fmt"
	"time"

	"github.com/jackc/pgx/v4/pgxpool"
)

type AnalyticsRepository struct {
	db *pgxpool.Pool
}

func NewAnalyticsRepository(db *pgxpool.Pool) *AnalyticsRepository {
	return &AnalyticsRepository{db: db}
}

// Overview Statistics
type OverviewStats struct {
	TotalUsers     int64
	TotalCourses   int64
	TotalThreads   int64
	ActiveStudents int64
}

func (r *AnalyticsRepository) GetOverviewStats(ctx context.Context, period string) (*OverviewStats, error) {
	var stats OverviewStats

	// Calculate date range based on period
	var startDate time.Time
	now := time.Now()

	switch period {
	case "last_7_days":
		startDate = now.AddDate(0, 0, -7)
	case "last_30_days":
		startDate = now.AddDate(0, 0, -30)
	case "last_90_days":
		startDate = now.AddDate(0, 0, -90)
	default:
		startDate = now.AddDate(0, 0, -30) // default to 30 days
	}

	// Get total users
	err := r.db.QueryRow(ctx, "SELECT COUNT(*) FROM users").Scan(&stats.TotalUsers)
	if err != nil {
		return nil, fmt.Errorf("failed to get total users: %w", err)
	}

	// Get total courses
	err = r.db.QueryRow(ctx, "SELECT COUNT(*) FROM courses").Scan(&stats.TotalCourses)
	if err != nil {
		return nil, fmt.Errorf("failed to get total courses: %w", err)
	}

	// Get total threads
	err = r.db.QueryRow(ctx, "SELECT COUNT(*) FROM threads").Scan(&stats.TotalThreads)
	if err != nil {
		return nil, fmt.Errorf("failed to get total threads: %w", err)
	}

	// Get active students (students with recent activity)
	query := `
		SELECT COUNT(DISTINCT tr.user_id) 
		FROM thread_registrations tr 
		JOIN users u ON u.id = tr.user_id 
		WHERE u.role = 'student' AND tr.registration_date >= $1
	`
	err = r.db.QueryRow(ctx, query, startDate).Scan(&stats.ActiveStudents)
	if err != nil {
		return nil, fmt.Errorf("failed to get active students: %w", err)
	}

	return &stats, nil
}

func (r *AnalyticsRepository) GetPreviousPeriodStats(ctx context.Context, period string) (*OverviewStats, error) {
	var stats OverviewStats

	// Calculate previous period dates
	var startDate, endDate time.Time
	now := time.Now()

	switch period {
	case "last_7_days":
		endDate = now.AddDate(0, 0, -7)
		startDate = now.AddDate(0, 0, -14)
	case "last_30_days":
		endDate = now.AddDate(0, 0, -30)
		startDate = now.AddDate(0, 0, -60)
	case "last_90_days":
		endDate = now.AddDate(0, 0, -90)
		startDate = now.AddDate(0, 0, -180)
	default:
		endDate = now.AddDate(0, 0, -30)
		startDate = now.AddDate(0, 0, -60)
	}

	// Get total users at end of previous period
	err := r.db.QueryRow(ctx, "SELECT COUNT(*) FROM users WHERE created_at <= $1", endDate).Scan(&stats.TotalUsers)
	if err != nil {
		return nil, fmt.Errorf("failed to get previous total users: %w", err)
	}

	// Get total courses at end of previous period
	err = r.db.QueryRow(ctx, "SELECT COUNT(*) FROM courses WHERE created_at <= $1", endDate).Scan(&stats.TotalCourses)
	if err != nil {
		return nil, fmt.Errorf("failed to get previous total courses: %w", err)
	}

	// Get total threads at end of previous period
	err = r.db.QueryRow(ctx, "SELECT COUNT(*) FROM threads WHERE created_at <= $1", endDate).Scan(&stats.TotalThreads)
	if err != nil {
		return nil, fmt.Errorf("failed to get previous total threads: %w", err)
	}

	// Get active students in previous period
	query := `
		SELECT COUNT(DISTINCT tr.user_id) 
		FROM thread_registrations tr 
		JOIN users u ON u.id = tr.user_id 
		WHERE u.role = 'student' AND tr.registration_date >= $1 AND tr.registration_date <= $2
	`
	err = r.db.QueryRow(ctx, query, startDate, endDate).Scan(&stats.ActiveStudents)
	if err != nil {
		return nil, fmt.Errorf("failed to get previous active students: %w", err)
	}

	return &stats, nil
}

// Timeline Data
type TimelinePoint struct {
	Date        string
	TotalUsers  int64
	NewUsers    int64
	ActiveUsers int64
}

func (r *AnalyticsRepository) GetUsersTimeline(ctx context.Context, period, granularity string) ([]*TimelinePoint, error) {
	var timeline []*TimelinePoint

	// Calculate date range and interval
	var startDate time.Time
	var interval string
	now := time.Now()

	switch period {
	case "7d":
		startDate = now.AddDate(0, 0, -7)
		interval = "1 day"
	case "30d":
		startDate = now.AddDate(0, 0, -30)
		interval = "1 day"
	case "90d":
		startDate = now.AddDate(0, 0, -90)
		interval = "1 day"
	case "1y":
		startDate = now.AddDate(-1, 0, 0)
		interval = "1 week"
	default:
		startDate = now.AddDate(0, 0, -30)
		interval = "1 day"
	}

	query := `
		WITH date_series AS (
			SELECT generate_series($1::date, $2::date, $3::interval) AS date
		),
		user_stats AS (
			SELECT 
				ds.date,
				COUNT(u.id) FILTER (WHERE u.created_at::date <= ds.date) AS total_users,
				COUNT(u.id) FILTER (WHERE u.created_at::date = ds.date) AS new_users,
				COUNT(DISTINCT tr.user_id) FILTER (WHERE tr.registration_date::date = ds.date) AS active_users
			FROM date_series ds
			LEFT JOIN users u ON u.created_at::date <= ds.date
			LEFT JOIN thread_registrations tr ON tr.registration_date::date = ds.date
			GROUP BY ds.date
			ORDER BY ds.date
		)
		SELECT 
			to_char(date, 'YYYY-MM-DD') as date,
			COALESCE(total_users, 0) as total_users,
			COALESCE(new_users, 0) as new_users,
			COALESCE(active_users, 0) as active_users
		FROM user_stats
	`

	rows, err := r.db.Query(ctx, query, startDate, now, interval)
	if err != nil {
		return nil, fmt.Errorf("failed to get users timeline: %w", err)
	}
	defer rows.Close()

	for rows.Next() {
		var point TimelinePoint
		err := rows.Scan(&point.Date, &point.TotalUsers, &point.NewUsers, &point.ActiveUsers)
		if err != nil {
			return nil, fmt.Errorf("failed to scan timeline point: %w", err)
		}
		timeline = append(timeline, &point)
	}

	return timeline, nil
}

// Demographics Data
type DemographicItem struct {
	Name       string
	Value      float64
	Percentage float64
}

func (r *AnalyticsRepository) GetUserDemographics(ctx context.Context) (devices []*DemographicItem, locations []*DemographicItem, err error) {
	// For now, return mock data since we don't have device/location tracking
	// In a real implementation, you would track this data

	devices = []*DemographicItem{
		{Name: "Windows", Value: 30, Percentage: 30.0},
		{Name: "Mac", Value: 25, Percentage: 25.0},
		{Name: "Linux", Value: 15, Percentage: 15.0},
		{Name: "iOS", Value: 20, Percentage: 20.0},
		{Name: "Android", Value: 15, Percentage: 15.0},
		{Name: "Other", Value: 5, Percentage: 5.0},
	}

	locations = []*DemographicItem{
		{Name: "Kazakhstan", Value: 52.1, Percentage: 52.1},
		{Name: "Russia", Value: 22.8, Percentage: 22.8},
		{Name: "USA", Value: 13.9, Percentage: 13.9},
		{Name: "Other", Value: 11.2, Percentage: 11.2},
	}

	return devices, locations, nil
}

// Recent Activities
type Activity struct {
	ID              string
	Type            string
	UserID          int64
	Name            string
	Email           string
	CourseID        *int64
	CourseTitle     *string
	AssignmentID    *int64
	AssignmentTitle *string
	Date            time.Time
	Status          string
}

func (r *AnalyticsRepository) GetRecentActivities(ctx context.Context, limit int32, activityType string) ([]*Activity, error) {
	var activities []*Activity

	// Base query for enrollments
	enrollmentQuery := `
		SELECT
			'ENR-' || tr.user_id || '-' || tr.thread_id as id,
			'enrollment' as type,
			u.id as user_id,
			u.name || ' ' || u.surname as name,
			u.email,
			c.id as course_id,
			c.title as course_title,
			NULL::bigint as assignment_id,
			NULL::text as assignment_title,
			tr.registration_date as date,
			'completed' as status
		FROM thread_registrations tr
		JOIN users u ON u.id = tr.user_id
		JOIN threads t ON t.id = tr.thread_id
		JOIN courses c ON c.id = t.course_id
		WHERE ($1 = 'all' OR $1 = 'enrollment')
		ORDER BY tr.registration_date DESC
		LIMIT $2
	`

	// Query for assignment submissions
	submissionQuery := `
		SELECT
			'SUB-' || sub.id as id,
			'assignment_submission' as type,
			u.id as user_id,
			u.name || ' ' || u.surname as name,
			u.email,
			NULL::bigint as course_id,
			NULL::text as course_title,
			a.id as assignment_id,
			a.title as assignment_title,
			sub.submitted_at as date,
			CASE
				WHEN sub.score IS NOT NULL THEN 'graded'
				ELSE 'pending'
			END as status
		FROM assignment_submissions sub
		JOIN assignments a ON a.id = sub.assignment_id
		JOIN users u ON u.id = sub.user_id
		WHERE ($1 = 'all' OR $1 = 'assignment')
		ORDER BY sub.submitted_at DESC
		LIMIT $2
	`

	var query string
	if activityType == "enrollment" {
		query = enrollmentQuery
	} else if activityType == "assignment" {
		query = submissionQuery
	} else {
		// Combine both queries with UNION
		query = fmt.Sprintf(`
			(%s) UNION ALL (%s)
			ORDER BY date DESC
			LIMIT $2
		`, enrollmentQuery, submissionQuery)
	}

	rows, err := r.db.Query(ctx, query, activityType, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get recent activities: %w", err)
	}
	defer rows.Close()

	for rows.Next() {
		var activity Activity
		err := rows.Scan(
			&activity.ID,
			&activity.Type,
			&activity.UserID,
			&activity.Name,
			&activity.Email,
			&activity.CourseID,
			&activity.CourseTitle,
			&activity.AssignmentID,
			&activity.AssignmentTitle,
			&activity.Date,
			&activity.Status,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan activity: %w", err)
		}
		activities = append(activities, &activity)
	}

	return activities, nil
}

// Performance Metrics
type PerformanceMetrics struct {
	CourseCompletionRate   float64
	AverageGrade           float64
	AttendanceRate         float64
	AverageSessionDuration string
}

func (r *AnalyticsRepository) GetPerformanceMetrics(ctx context.Context) (*PerformanceMetrics, error) {
	var metrics PerformanceMetrics

	// Course completion rate (threads with final grades / total registrations)
	completionQuery := `
		SELECT
			COALESCE(
				(COUNT(*) FILTER (WHERE final_grade IS NOT NULL)::float /
				 NULLIF(COUNT(*)::float, 0)) * 100,
				0
			) as completion_rate
		FROM thread_registrations
	`
	err := r.db.QueryRow(ctx, completionQuery).Scan(&metrics.CourseCompletionRate)
	if err != nil {
		return nil, fmt.Errorf("failed to get completion rate: %w", err)
	}

	// Average grade across all submissions
	gradeQuery := `
		SELECT COALESCE(AVG(score), 0) as avg_grade
		FROM assignment_submissions
		WHERE score IS NOT NULL
	`
	err = r.db.QueryRow(ctx, gradeQuery).Scan(&metrics.AverageGrade)
	if err != nil {
		return nil, fmt.Errorf("failed to get average grade: %w", err)
	}

	// Attendance rate (present / total attendance records)
	attendanceQuery := `
		SELECT
			COALESCE(
				(COUNT(*) FILTER (WHERE status = 'present')::float /
				 NULLIF(COUNT(*)::float, 0)) * 100,
				0
			) as attendance_rate
		FROM attendance
		WHERE status != 'unmarked'
	`
	err = r.db.QueryRow(ctx, attendanceQuery).Scan(&metrics.AttendanceRate)
	if err != nil {
		return nil, fmt.Errorf("failed to get attendance rate: %w", err)
	}

	// Mock session duration for now
	metrics.AverageSessionDuration = "2m 45s"

	return &metrics, nil
}

// Detailed Stats
type CourseEnrollmentPoint struct {
	Date        string
	Enrollments int64
	Completions int64
}

type UserActivityDay struct {
	Day         string
	ActiveUsers int64
}

type TrafficSource struct {
	Source     string
	Value      float64
	Percentage float64
}

func (r *AnalyticsRepository) GetCourseEnrollments(ctx context.Context, period string) ([]*CourseEnrollmentPoint, error) {
	var enrollments []*CourseEnrollmentPoint

	var startDate time.Time
	now := time.Now()

	switch period {
	case "7d":
		startDate = now.AddDate(0, 0, -7)
	case "30d":
		startDate = now.AddDate(0, 0, -30)
	case "90d":
		startDate = now.AddDate(0, 0, -90)
	case "1y":
		startDate = now.AddDate(-1, 0, 0)
	default:
		startDate = now.AddDate(0, 0, -30)
	}

	query := `
		WITH date_series AS (
			SELECT generate_series($1::date, $2::date, '1 day'::interval) AS date
		)
		SELECT
			to_char(ds.date, 'YYYY-MM-DD') as date,
			COUNT(tr.user_id) FILTER (WHERE tr.registration_date::date = ds.date) as enrollments,
			COUNT(tr.user_id) FILTER (WHERE tr.final_grade IS NOT NULL AND tr.registration_date::date <= ds.date) as completions
		FROM date_series ds
		LEFT JOIN thread_registrations tr ON tr.registration_date::date = ds.date
		GROUP BY ds.date
		ORDER BY ds.date
	`

	rows, err := r.db.Query(ctx, query, startDate, now)
	if err != nil {
		return nil, fmt.Errorf("failed to get course enrollments: %w", err)
	}
	defer rows.Close()

	for rows.Next() {
		var point CourseEnrollmentPoint
		err := rows.Scan(&point.Date, &point.Enrollments, &point.Completions)
		if err != nil {
			return nil, fmt.Errorf("failed to scan enrollment point: %w", err)
		}
		enrollments = append(enrollments, &point)
	}

	return enrollments, nil
}

func (r *AnalyticsRepository) GetUserActivityByDay(ctx context.Context) ([]*UserActivityDay, error) {
	// Mock data for weekly activity
	return []*UserActivityDay{
		{Day: "Monday", ActiveUsers: 420},
		{Day: "Tuesday", ActiveUsers: 380},
		{Day: "Wednesday", ActiveUsers: 450},
		{Day: "Thursday", ActiveUsers: 520},
		{Day: "Friday", ActiveUsers: 490},
		{Day: "Saturday", ActiveUsers: 380},
		{Day: "Sunday", ActiveUsers: 320},
	}, nil
}

func (r *AnalyticsRepository) GetTrafficSources(ctx context.Context) ([]*TrafficSource, error) {
	// Mock data for traffic sources
	return []*TrafficSource{
		{Source: "Direct", Value: 35, Percentage: 35.0},
		{Source: "Organic Search", Value: 25, Percentage: 25.0},
		{Source: "Referral", Value: 15, Percentage: 15.0},
		{Source: "Social Media", Value: 15, Percentage: 15.0},
		{Source: "Email", Value: 8, Percentage: 8.0},
		{Source: "Other", Value: 2, Percentage: 2.0},
	}, nil
}
