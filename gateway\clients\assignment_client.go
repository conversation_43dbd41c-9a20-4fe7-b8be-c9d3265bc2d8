package clients

import (
	"context"

	"google.golang.org/protobuf/types/known/timestamppb"

	assignmentpb "github.com/olzzhas/edunite-server/course_service/pb/assignment"
)

type AssignmentClient struct {
	client assignmentpb.AssignmentServiceClient
}

func NewAssignmentClient(client assignmentpb.AssignmentServiceClient) *AssignmentClient {
	return &AssignmentClient{client: client}
}

// CreateAssignmentGroup creates a new assignment group
func (c *AssignmentClient) CreateAssignmentGroup(ctx context.Context, threadID int64, name, groupType string, weight float32) (*assignmentpb.AssignmentGroupResponse, error) {
	req := &assignmentpb.AssignmentGroupRequest{
		ThreadId:  threadID,
		Name:      name,
		GroupType: groupType,
		Weight:    weight,
	}
	return c.client.CreateAssignmentGroup(ctx, req)
}

// ListAssignmentGroupsForThread lists all groups for a thread
func (c *AssignmentClient) ListAssignmentGroupsForThread(ctx context.Context, threadID int64) ([]*assignmentpb.AssignmentGroupResponse, error) {
	req := &assignmentpb.AssignmentGroupsForThread{ThreadId: threadID}
	resp, err := c.client.ListAssignmentGroupsForThread(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp.GetAssignmentGroups(), nil
}

// GetAssignmentGroupByID fetches a group by its ID
func (c *AssignmentClient) GetAssignmentGroupByID(ctx context.Context, id int64) (*assignmentpb.AssignmentGroupResponse, error) {
	req := &assignmentpb.AssignmentGroupByID{AssignmentId: id}
	return c.client.GetAssignmentGroupByID(ctx, req)
}

// UpdateAssignmentGroupByID updates an existing group
func (c *AssignmentClient) UpdateAssignmentGroupByID(ctx context.Context, id, threadID int64, name, groupType string, weight float32) (*assignmentpb.AssignmentGroupResponse, error) {
	req := &assignmentpb.AssignmentGroupUpdateRequest{
		Id:        id,
		ThreadId:  threadID,
		Name:      name,
		GroupType: groupType,
		Weight:    weight,
	}
	return c.client.UpdateAssignmentGroupByID(ctx, req)
}

// DeleteAssignmentGroupByID deletes a group by its ID
func (c *AssignmentClient) DeleteAssignmentGroupByID(ctx context.Context, id int64) error {
	req := &assignmentpb.AssignmentGroupByID{AssignmentId: id}
	_, err := c.client.DeleteAssignmentGroupByID(ctx, req)
	return err
}

// CreateAssignmentAttachment uploads a new attachment
func (c *AssignmentClient) CreateAssignmentAttachment(ctx context.Context, assignmentID int64, fileURL string) (*assignmentpb.AssignmentAttachmentResponse, error) {
	req := &assignmentpb.CreateAssignmentAttachmentRequest{
		AssignmentId: assignmentID,
		FileUrl:      fileURL,
	}
	return c.client.CreateAssignmentAttachment(ctx, req)
}

// GetAssignmentAttachmentsByAssignmentID retrieves attachments for an assignment
func (c *AssignmentClient) GetAssignmentAttachmentsByAssignmentID(ctx context.Context, assignmentID int64) ([]*assignmentpb.AssignmentAttachmentResponse, error) {
	req := &assignmentpb.AssignmentIDRequest{AssignmentId: assignmentID}
	resp, err := c.client.GetAssignmentAttachmentsByAssignmentID(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp.GetAttachments(), nil
}

// DeleteAssignmentAttachmentByID removes an attachment by its ID
func (c *AssignmentClient) DeleteAssignmentAttachmentByID(ctx context.Context, id int64) error {
	req := &assignmentpb.AssignmentAttachmentByIDRequest{AttachmentId: id}
	_, err := c.client.DeleteAssignmentAttachmentByID(ctx, req)
	return err
}

// CreateAssignmentSubmission creates a new submission
func (c *AssignmentClient) CreateAssignmentSubmission(ctx context.Context, assignmentID, userID int64, fileURLs []string, comment string) (*assignmentpb.AssignmentSubmissionResponse, error) {
	req := &assignmentpb.CreateAssignmentSubmissionRequest{
		AssignmentId: assignmentID,
		UserId:       userID,
		FileUrls:     fileURLs,
		Comment:      comment,
	}
	return c.client.CreateAssignmentSubmission(ctx, req)
}

// GetAssignmentSubmissionByID fetches a submission by its ID
func (c *AssignmentClient) GetAssignmentSubmissionByID(ctx context.Context, id int64) (*assignmentpb.AssignmentSubmissionResponse, error) {
	req := &assignmentpb.AssignmentSubmissionByIDRequest{SubmissionId: id}
	return c.client.GetAssignmentSubmissionByID(ctx, req)
}

// ListAssignmentSubmissionsByAssignmentID lists all submissions for an assignment
func (c *AssignmentClient) ListAssignmentSubmissionsByAssignmentID(ctx context.Context, assignmentID int64) ([]*assignmentpb.AssignmentSubmissionResponse, error) {
	req := &assignmentpb.AssignmentIDRequest{AssignmentId: assignmentID}
	resp, err := c.client.ListAssignmentSubmissionsByAssignmentID(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp.GetSubmissions(), nil
}

// UpdateAssignmentSubmissionScore updates score and feedback for a submission
func (c *AssignmentClient) UpdateAssignmentSubmissionScore(ctx context.Context, submissionID int64, score int32, feedback string) (*assignmentpb.AssignmentSubmissionResponse, error) {
	req := &assignmentpb.UpdateAssignmentSubmissionScoreRequest{
		SubmissionId: submissionID,
		Score:        score,
		Feedback:     feedback,
	}
	return c.client.UpdateAssignmentSubmissionScore(ctx, req)
}

// UpdateAssignmentSubmission updates an existing submission with new files and comment
func (c *AssignmentClient) UpdateAssignmentSubmission(ctx context.Context, submissionID, assignmentID, userID int64, fileURLs []string, comment string) (*assignmentpb.AssignmentSubmissionResponse, error) {
	// Since there's no direct update method in the service, we'll use the existing submission repository's UpdateSubmission method
	// First, create a new submission request with the same assignment and user IDs
	req := &assignmentpb.CreateAssignmentSubmissionRequest{
		AssignmentId: assignmentID,
		UserId:       userID,
		FileUrls:     fileURLs,
		Comment:      comment,
	}

	// The service will detect this is an existing submission and update it
	return c.client.CreateAssignmentSubmission(ctx, req)
}

// DeleteAssignmentSubmissionByID deletes a submission by its ID
func (c *AssignmentClient) DeleteAssignmentSubmissionByID(ctx context.Context, id int64) error {
	req := &assignmentpb.AssignmentSubmissionByIDRequest{SubmissionId: id}
	_, err := c.client.DeleteAssignmentSubmissionByID(ctx, req)
	return err
}

// CreateAssignment создаёт новую задачу
func (c *AssignmentClient) CreateAssignment(
	ctx context.Context,
	weekID int64,
	title, description string,
	dueDate *timestamppb.Timestamp, // или time.Time в зависимости от вашей обёртки
	maxPoints int32,
	groupID int64,
	assignmentType string,
) (*assignmentpb.AssignmentResponse, error) {
	req := &assignmentpb.AssignmentRequest{
		WeekId:            weekID,
		Title:             title,
		Description:       description,
		DueDate:           dueDate,
		MaxPoints:         maxPoints,
		AssignmentGroupId: groupID,
		Type:              assignmentType,
	}
	return c.client.CreateAssignment(ctx, req)
}

// ListAssignmentsForWeek возвращает все задачи указанной недели
func (c *AssignmentClient) ListAssignmentsForWeek(
	ctx context.Context,
	weekID int64,
) ([]*assignmentpb.AssignmentResponse, error) {
	req := &assignmentpb.AssignmentsForWeekRequest{WeekId: weekID}
	resp, err := c.client.ListAssignmentsForWeek(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp.GetAssignments(), nil
}

// GetAssignmentByID получает задачу по её ID
func (c *AssignmentClient) GetAssignmentByID(
	ctx context.Context,
	id int64,
) (*assignmentpb.AssignmentResponse, error) {
	req := &assignmentpb.AssignmentByID{Id: id}
	return c.client.GetAssignmentByID(ctx, req)
}

// UpdateAssignmentByID обновляет существующую задачу
func (c *AssignmentClient) UpdateAssignmentByID(
	ctx context.Context,
	id, weekID int64,
	title, description string,
	dueDate *timestamppb.Timestamp,
	maxPoints int32,
	groupID int64,
	assignmentType string,
) (*assignmentpb.AssignmentResponse, error) {
	req := &assignmentpb.AssignmentUpdateRequest{
		Id:                id,
		WeekId:            weekID,
		Title:             title,
		Description:       description,
		DueDate:           dueDate,
		MaxPoints:         maxPoints,
		AssignmentGroupId: groupID,
		Type:              assignmentType,
	}
	return c.client.UpdateAssignmentByID(ctx, req)
}

// DeleteAssignmentByID удаляет задачу по её ID
func (c *AssignmentClient) DeleteAssignmentByID(
	ctx context.Context,
	id int64,
) error {
	req := &assignmentpb.AssignmentByID{Id: id}
	_, err := c.client.DeleteAssignmentByID(ctx, req)
	return err
}

func (c *AssignmentClient) ListAssignmentsWithSubmissionForThread(
	ctx context.Context,
	threadID, userID int64,
) ([]*assignmentpb.AssignmentWithSubmission, error) {
	req := &assignmentpb.AssignmentsWithSubmissionRequest{
		ThreadId: threadID,
		UserId:   userID,
	}
	resp, err := c.client.ListAssignmentsWithSubmissionForThread(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp.GetItems(), nil
}

// ListAssignmentsWithoutSubmissionForThread возвращает все задания потока, для которых у студента нет сабмишенов
func (c *AssignmentClient) ListAssignmentsWithoutSubmissionForThread(
	ctx context.Context,
	threadID, userID int64,
) ([]*assignmentpb.AssignmentWithSubmission, error) {
	req := &assignmentpb.AssignmentsWithSubmissionRequest{
		ThreadId: threadID,
		UserId:   userID,
	}
	resp, err := c.client.ListAssignmentsWithoutSubmissionForThread(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp.GetItems(), nil
}

// GetAssignmentDetailsForStudent fetches comprehensive assignment details for a student
func (c *AssignmentClient) GetAssignmentDetailsForStudent(
	ctx context.Context,
	assignmentID, studentID int64,
) (*assignmentpb.AssignmentDetailsForStudentResponse, error) {
	req := &assignmentpb.AssignmentDetailsForStudentRequest{
		AssignmentId: assignmentID,
		StudentId:    studentID,
	}
	return c.client.GetAssignmentDetailsForStudent(ctx, req)
}

// GetThreadGradebook fetches the gradebook for a thread with all assignments and student grades
func (c *AssignmentClient) GetThreadGradebook(
	ctx context.Context,
	threadID int64,
) (*assignmentpb.ThreadGradebookResponse, error) {
	req := &assignmentpb.ThreadGradebookRequest{
		ThreadId: threadID,
	}
	return c.client.GetThreadGradebook(ctx, req)
}
