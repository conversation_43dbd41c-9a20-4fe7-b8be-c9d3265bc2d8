package service

import (
	"context"
	"fmt"
	"log"
	"strconv"
	"time"

	"github.com/olzzhas/edunite-server/notification_service/internal/email"
	"github.com/olzzhas/edunite-server/notification_service/internal/models"
	"github.com/olzzhas/edunite-server/notification_service/internal/repository"
)

type NotificationService interface {
	CreateNotification(ctx context.Context, req *models.CreateNotificationRequest) (*models.Notification, error)
	GetUserNotifications(ctx context.Context, userID int64, page, limit int32, unreadOnly bool) ([]*models.UserNotification, int32, int32, error)
	GetAllNotifications(ctx context.Context, page, limit int32, targetType models.TargetType, targetValue string) ([]*models.Notification, int32, error)
	MarkAsRead(ctx context.Context, notificationID, userID int64) error
	DeleteNotification(ctx context.Context, notificationID int64) error
	SendScheduledNotifications(ctx context.Context) (int32, error)
	GetNotificationStats(ctx context.Context, userID int64) (*models.NotificationStats, error)
	GetEmailTemplates(ctx context.Context, activeOnly bool) ([]*models.EmailTemplate, int32, error)
}

type notificationService struct {
	repo         repository.NotificationRepository
	emailService email.EmailService
}

func NewNotificationService(repo repository.NotificationRepository, emailService email.EmailService) NotificationService {
	return &notificationService{
		repo:         repo,
		emailService: emailService,
	}
}

func (s *notificationService) CreateNotification(ctx context.Context, req *models.CreateNotificationRequest) (*models.Notification, error) {
	// Create notification record
	notification := &models.Notification{
		Title:         req.Title,
		Message:       req.Message,
		Type:          req.Type,
		Priority:      req.Priority,
		TargetType:    req.TargetType,
		TargetValue:   req.TargetValue,
		SenderID:      req.SenderID,
		SendEmail:     req.SendEmail,
		EmailSubject:  req.EmailSubject,
		EmailTemplate: req.EmailTemplate,
		ScheduledAt:   req.ScheduledAt,
	}

	// Save notification to database
	createdNotification, err := s.repo.CreateNotification(ctx, notification)
	if err != nil {
		return nil, fmt.Errorf("failed to create notification: %w", err)
	}

	// If not scheduled, send immediately
	if req.ScheduledAt == nil || req.ScheduledAt.Before(time.Now()) {
		if err := s.processNotification(ctx, createdNotification); err != nil {
			log.Printf("Failed to process notification %d: %v", createdNotification.ID, err)
			// Don't return error here as notification was created successfully
		}
	}

	return createdNotification, nil
}

func (s *notificationService) GetUserNotifications(ctx context.Context, userID int64, page, limit int32, unreadOnly bool) ([]*models.UserNotification, int32, int32, error) {
	if page <= 0 {
		page = 1
	}
	if limit <= 0 || limit > 100 {
		limit = 20
	}

	return s.repo.GetUserNotifications(ctx, userID, page, limit, unreadOnly)
}

func (s *notificationService) GetAllNotifications(ctx context.Context, page, limit int32, targetType models.TargetType, targetValue string) ([]*models.Notification, int32, error) {
	if page <= 0 {
		page = 1
	}
	if limit <= 0 || limit > 100 {
		limit = 20
	}

	return s.repo.GetAllNotifications(ctx, page, limit, targetType, targetValue)
}

func (s *notificationService) MarkAsRead(ctx context.Context, notificationID, userID int64) error {
	return s.repo.MarkAsRead(ctx, notificationID, userID)
}

func (s *notificationService) DeleteNotification(ctx context.Context, notificationID int64) error {
	return s.repo.DeleteNotification(ctx, notificationID)
}

func (s *notificationService) SendScheduledNotifications(ctx context.Context) (int32, error) {
	notifications, err := s.repo.GetScheduledNotifications(ctx)
	if err != nil {
		return 0, fmt.Errorf("failed to get scheduled notifications: %w", err)
	}

	var sentCount int32
	for _, notification := range notifications {
		if err := s.processNotification(ctx, notification); err != nil {
			log.Printf("Failed to process scheduled notification %d: %v", notification.ID, err)
			continue
		}
		sentCount++
	}

	return sentCount, nil
}

func (s *notificationService) GetNotificationStats(ctx context.Context, userID int64) (*models.NotificationStats, error) {
	return s.repo.GetNotificationStats(ctx, userID)
}

func (s *notificationService) GetEmailTemplates(ctx context.Context, activeOnly bool) ([]*models.EmailTemplate, int32, error) {
	return s.repo.GetEmailTemplates(ctx, activeOnly)
}

// processNotification handles the actual sending of notifications
func (s *notificationService) processNotification(ctx context.Context, notification *models.Notification) error {
	// Get target users
	users, err := s.getTargetUsers(ctx, notification)
	if err != nil {
		return fmt.Errorf("failed to get target users: %w", err)
	}

	if len(users) == 0 {
		log.Printf("No target users found for notification %d", notification.ID)
		return s.repo.MarkAsSent(ctx, notification.ID)
	}

	// Extract user IDs
	userIDs := make([]int64, len(users))
	for i, user := range users {
		userIDs[i] = user.ID
	}

	// Create notification recipients
	if err := s.repo.CreateRecipients(ctx, notification.ID, userIDs); err != nil {
		return fmt.Errorf("failed to create recipients: %w", err)
	}

	// Send emails if required
	if notification.SendEmail {
		if err := s.sendNotificationEmails(ctx, notification, users); err != nil {
			log.Printf("Failed to send emails for notification %d: %v", notification.ID, err)
			// Don't return error here as notification was created successfully
		}
	}

	// Mark notification as sent
	return s.repo.MarkAsSent(ctx, notification.ID)
}

// getTargetUsers returns users based on notification target type
func (s *notificationService) getTargetUsers(ctx context.Context, notification *models.Notification) ([]*models.User, error) {
	switch notification.TargetType {
	case models.TargetTypeAll:
		return s.repo.GetAllUsers(ctx)

	case models.TargetTypeRole:
		if notification.TargetValue == nil {
			return nil, fmt.Errorf("target value is required for role target type")
		}
		return s.repo.GetUsersByRole(ctx, *notification.TargetValue)

	case models.TargetTypeUser:
		if notification.TargetValue == nil {
			return nil, fmt.Errorf("target value is required for user target type")
		}
		userID, err := strconv.ParseInt(*notification.TargetValue, 10, 64)
		if err != nil {
			return nil, fmt.Errorf("invalid user ID: %w", err)
		}
		user, err := s.repo.GetUserByID(ctx, userID)
		if err != nil {
			return nil, err
		}
		return []*models.User{user}, nil

	case models.TargetTypeDegree:
		if notification.TargetValue == nil {
			return nil, fmt.Errorf("target value is required for degree target type")
		}
		degreeID, err := strconv.ParseInt(*notification.TargetValue, 10, 64)
		if err != nil {
			return nil, fmt.Errorf("invalid degree ID: %w", err)
		}
		return s.repo.GetUsersByDegree(ctx, degreeID)

	case models.TargetTypeCourse:
		if notification.TargetValue == nil {
			return nil, fmt.Errorf("target value is required for course target type")
		}
		courseID, err := strconv.ParseInt(*notification.TargetValue, 10, 64)
		if err != nil {
			return nil, fmt.Errorf("invalid course ID: %w", err)
		}
		return s.repo.GetUsersByCourse(ctx, courseID)

	case models.TargetTypeThread:
		if notification.TargetValue == nil {
			return nil, fmt.Errorf("target value is required for thread target type")
		}
		threadID, err := strconv.ParseInt(*notification.TargetValue, 10, 64)
		if err != nil {
			return nil, fmt.Errorf("invalid thread ID: %w", err)
		}
		return s.repo.GetUsersByThread(ctx, threadID)

	default:
		return nil, fmt.Errorf("unsupported target type: %s", notification.TargetType)
	}
}

// sendNotificationEmails sends email notifications to users
func (s *notificationService) sendNotificationEmails(ctx context.Context, notification *models.Notification, users []*models.User) error {
	// Get email template
	var emailTemplate *models.EmailTemplate
	var err error

	// Use template specified in notification, or choose default based on type/priority
	templateName := "default_notification"
	if notification.EmailTemplate != nil && *notification.EmailTemplate != "" {
		templateName = *notification.EmailTemplate
	} else if notification.Priority == models.NotificationPriorityUrgent {
		templateName = "urgent_notification"
	} else if notification.Type == models.NotificationTypeAnnouncement {
		templateName = "announcement"
	}

	emailTemplate, err = s.repo.GetEmailTemplate(ctx, templateName)
	if err != nil {
		log.Printf("Failed to get email template %s: %v", templateName, err)
		// Try to get default template as fallback
		if templateName != "default_notification" {
			emailTemplate, err = s.repo.GetEmailTemplate(ctx, "default_notification")
			if err != nil {
				log.Printf("Failed to get default email template: %v", err)
			}
		}
	}

	// Send emails
	return s.emailService.SendBulkNotificationEmails(notification, users, emailTemplate)
}
