// internal/assignmentquery/repo.go
package assignmentquery

import (
	"context"
	"fmt"

	"github.com/olzzhas/edunite-server/course_service/internal/dto"

	"github.com/jackc/pgx/v4/pgxpool"
)

type Repo struct{ db *pgxpool.Pool }

func NewRepo(db *pgxpool.Pool) *Repo { return &Repo{db: db} }

// ListAssignmentsWithSubmission возвращает все задания потока и, если есть, submission указанного user_id
func (r *Repo) ListAssignmentsWithSubmission(ctx context.Context, threadID, userID int64) ([]*dto.AssignmentWithSubmissionDTO, error) {
	const q = `
		SELECT
		  a.id         AS ass_id,
		  w.id         AS week_id,
		  a.title,
		  a.description,
		  a.due_date,
		  a.max_points,
		  a.assignment_group_id,
		  a.type,
		  a.created_at AS ass_created,
		  a.updated_at AS ass_updated,

		  s.id         AS sub_id,
		  s.user_id,
		  s.submitted_at,
		  s.file_urls,
		  s.comment,
		  s.score,
		  s.feedback,
		  s.created_at AS sub_created,
		  s.updated_at AS sub_updated
		FROM assignments a
		JOIN weeks w
		  ON a.week_id = w.id
		LEFT JOIN assignment_submissions s
		  ON s.assignment_id = a.id
		  AND s.user_id      = $2
		WHERE w.thread_id = $1
		ORDER BY w.week_number, a.id;
	`
	rows, err := r.db.Query(ctx, q, threadID, userID)
	if err != nil {
		return nil, fmt.Errorf("query assignments with submission: %w", err)
	}
	defer rows.Close()

	var out []*dto.AssignmentWithSubmissionDTO
	for rows.Next() {
		var d dto.AssignmentWithSubmissionDTO
		if err := rows.Scan(
			&d.AssID, &d.WeekID, &d.Title, &d.Description, &d.DueDate, &d.MaxPoints, &d.AssignmentGroupID, &d.Type,
			&d.AssCreatedAt, &d.AssUpdatedAt,
			&d.SubID, &d.UserID, &d.SubmittedAt, &d.FileURLs, &d.Comment, &d.Score, &d.Feedback,
			&d.SubCreatedAt, &d.SubUpdatedAt,
		); err != nil {
			return nil, fmt.Errorf("scan row: %w", err)
		}
		out = append(out, &d)
	}
	return out, rows.Err()
}

// ListAssignmentsWithoutSubmission возвращает все задания потока, для которых у указанного user_id нет submission
func (r *Repo) ListAssignmentsWithoutSubmission(ctx context.Context, threadID, userID int64) ([]*dto.AssignmentWithSubmissionDTO, error) {
	const q = `
		SELECT
		  a.id         AS ass_id,
		  w.id         AS week_id,
		  a.title,
		  a.description,
		  a.due_date,
		  a.max_points,
		  a.assignment_group_id,
		  a.type,
		  a.created_at AS ass_created,
		  a.updated_at AS ass_updated,
		  NULL         AS sub_id,
		  NULL         AS user_id,
		  NULL         AS submitted_at,
		  NULL         AS file_urls,
		  NULL         AS comment,
		  NULL         AS score,
		  NULL         AS feedback,
		  NULL         AS sub_created,
		  NULL         AS sub_updated
		FROM assignments a
		JOIN weeks w ON a.week_id = w.id
		LEFT JOIN assignment_submissions s ON s.assignment_id = a.id AND s.user_id = $2
		WHERE w.thread_id = $1 AND s.id IS NULL
		ORDER BY w.week_number, a.id;
	`
	rows, err := r.db.Query(ctx, q, threadID, userID)
	if err != nil {
		return nil, fmt.Errorf("query assignments without submission: %w", err)
	}
	defer rows.Close()

	var out []*dto.AssignmentWithSubmissionDTO
	for rows.Next() {
		var d dto.AssignmentWithSubmissionDTO
		var fileURLs []string // Empty array for NULL file_urls
		if err := rows.Scan(
			&d.AssID, &d.WeekID, &d.Title, &d.Description, &d.DueDate, &d.MaxPoints, &d.AssignmentGroupID, &d.Type,
			&d.AssCreatedAt, &d.AssUpdatedAt,
			&d.SubID, &d.UserID, &d.SubmittedAt, &fileURLs, &d.Comment, &d.Score, &d.Feedback,
			&d.SubCreatedAt, &d.SubUpdatedAt,
		); err != nil {
			return nil, fmt.Errorf("scan row: %w", err)
		}
		d.FileURLs = fileURLs
		out = append(out, &d)
	}
	return out, rows.Err()
}

// GetThreadGradebook возвращает все задания потока и оценки всех студентов
func (r *Repo) GetThreadGradebook(ctx context.Context, threadID int64) (*dto.ThreadGradebookDTO, error) {
	// Сначала получаем все задания потока
	const assignmentsQuery = `
		SELECT
		  a.id,
		  a.title,
		  a.max_points
		FROM assignments a
		JOIN weeks w ON a.week_id = w.id
		WHERE w.thread_id = $1 AND a.type = 'task'
		ORDER BY w.week_number, a.id;
	`

	assignmentRows, err := r.db.Query(ctx, assignmentsQuery, threadID)
	if err != nil {
		return nil, fmt.Errorf("query assignments: %w", err)
	}
	defer assignmentRows.Close()

	var assignments []*dto.AssignmentInfoDTO
	for assignmentRows.Next() {
		var assignment dto.AssignmentInfoDTO
		if err := assignmentRows.Scan(&assignment.ID, &assignment.Title, &assignment.MaxPoints); err != nil {
			return nil, fmt.Errorf("scan assignment: %w", err)
		}
		assignments = append(assignments, &assignment)
	}
	if err := assignmentRows.Err(); err != nil {
		return nil, fmt.Errorf("assignments rows error: %w", err)
	}

	// Затем получаем всех студентов потока с их оценками
	const studentsQuery = `
		SELECT DISTINCT
		  tr.user_id,
		  u.name,
		  u.surname,
		  tr.final_grade
		FROM thread_registrations tr
		JOIN users u ON tr.user_id = u.id
		WHERE tr.thread_id = $1 AND u.role = 'student'
		ORDER BY u.surname, u.name;
	`

	studentRows, err := r.db.Query(ctx, studentsQuery, threadID)
	if err != nil {
		return nil, fmt.Errorf("query students: %w", err)
	}
	defer studentRows.Close()

	var students []*dto.StudentGradesDTO
	for studentRows.Next() {
		var student dto.StudentGradesDTO
		if err := studentRows.Scan(&student.ID, &student.Name, &student.Surname, &student.FinalGrade); err != nil {
			return nil, fmt.Errorf("scan student: %w", err)
		}
		students = append(students, &student)
	}
	if err := studentRows.Err(); err != nil {
		return nil, fmt.Errorf("students rows error: %w", err)
	}

	// Получаем все оценки для всех студентов и заданий
	const gradesQuery = `
		SELECT
		  s.user_id,
		  s.assignment_id,
		  s.score
		FROM assignment_submissions s
		JOIN assignments a ON s.assignment_id = a.id
		JOIN weeks w ON a.week_id = w.id
		WHERE w.thread_id = $1 AND s.score IS NOT NULL;
	`

	gradeRows, err := r.db.Query(ctx, gradesQuery, threadID)
	if err != nil {
		return nil, fmt.Errorf("query grades: %w", err)
	}
	defer gradeRows.Close()

	// Создаем карту для быстрого поиска оценок
	gradeMap := make(map[int64]map[int64]int32) // userID -> assignmentID -> score
	for gradeRows.Next() {
		var userID, assignmentID int64
		var score int32
		if err := gradeRows.Scan(&userID, &assignmentID, &score); err != nil {
			return nil, fmt.Errorf("scan grade: %w", err)
		}

		if gradeMap[userID] == nil {
			gradeMap[userID] = make(map[int64]int32)
		}
		gradeMap[userID][assignmentID] = score
	}
	if err := gradeRows.Err(); err != nil {
		return nil, fmt.Errorf("grades rows error: %w", err)
	}

	// Заполняем оценки для каждого студента
	for _, student := range students {
		student.Grades = make([]*dto.GradeDTO, 0, len(assignments))
		for _, assignment := range assignments {
			grade := &dto.GradeDTO{
				AssignmentID: assignment.ID,
			}

			if userGrades, exists := gradeMap[student.ID]; exists {
				if score, hasGrade := userGrades[assignment.ID]; hasGrade {
					grade.Score = &score
				}
			}

			student.Grades = append(student.Grades, grade)
		}
	}

	return &dto.ThreadGradebookDTO{
		Assignments: assignments,
		Students:    students,
	}, nil
}
