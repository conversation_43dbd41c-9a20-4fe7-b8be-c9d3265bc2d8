package clients

import (
	"context"
	"time"

	attendancepb "github.com/olzzhas/edunite-server/course_service/pb/attendance"
	"google.golang.org/protobuf/types/known/timestamppb"

	"google.golang.org/grpc"
)

// AttendanceClient wraps the gRPC AttendanceServiceClient
type AttendanceClient struct {
	client attendancepb.AttendanceServiceClient
}

// Option NewAttendanceClient creates a new AttendanceClient
type Option func(*AttendanceClient)

func NewAttendanceClient(conn *grpc.ClientConn) *AttendanceClient {
	return &AttendanceClient{client: attendancepb.NewAttendanceServiceClient(conn)}
}

// CreateAttendanceParams CreateAttendance creates a new attendance record
type CreateAttendanceParams struct {
	ThreadID       int64
	UserID         int64
	AttendanceDate time.Time
	Status         attendancepb.AttendanceStatus
	Reason         string
}

func (c *AttendanceClient) CreateAttendance(ctx context.Context, params CreateAttendanceParams) (*attendancepb.AttendanceResponse, error) {
	req := &attendancepb.AttendanceRequest{
		ThreadId:       params.ThreadID,
		UserId:         params.UserID,
		AttendanceDate: timestamppb.New(params.AttendanceDate),
		Status:         params.Status,
		Reason:         params.Reason,
	}
	return c.client.CreateAttendance(ctx, req)
}

// ListAttendance lists attendance records for a thread on a specific date
func (c *AttendanceClient) ListAttendance(ctx context.Context, threadID int64, date time.Time) ([]*attendancepb.AttendanceWithUserResponse, error) {
	req := &attendancepb.ListAttendanceRequest{
		ThreadId:       threadID,
		AttendanceDate: timestamppb.New(date),
	}
	resp, err := c.client.ListAttendance(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp.GetRecords(), nil
}

// UpdateAttendance updates status or reason of an attendance record
func (c *AttendanceClient) UpdateAttendance(ctx context.Context, id int64, status attendancepb.AttendanceStatus, reason string) (*attendancepb.AttendanceResponse, error) {
	req := &attendancepb.UpdateAttendanceRequest{
		Id:     id,
		Status: status,
		Reason: reason,
	}
	return c.client.UpdateAttendance(ctx, req)
}

// DeleteAttendanceByID deletes an attendance record by its ID
func (c *AttendanceClient) DeleteAttendanceByID(ctx context.Context, id int64) error {
	req := &attendancepb.AttendanceIDRequest{Id: id}
	_, err := c.client.DeleteAttendanceByID(ctx, req)
	return err
}

// DeleteAttendancesByThreadUser deletes attendances of user by thread
func (c *AttendanceClient) DeleteAttendancesByThreadUser(ctx context.Context, threadID, userID int64) error {
	req := &attendancepb.AttendancesByThreadUser{
		ThreadId: threadID,
		UserId:   userID,
	}
	_, err := c.client.DeleteAttendancesByThreadUser(ctx, req)
	return err
}
