-- Create notification type enum
CREATE TYPE notification_type AS ENUM (
    'info',
    'warning', 
    'success',
    'error',
    'announcement'
);

-- Create notification priority enum
CREATE TYPE notification_priority AS ENUM (
    'low',
    'normal',
    'high',
    'urgent'
);

-- Create target type enum for notification recipients
CREATE TYPE target_type AS ENUM (
    'all',
    'role',
    'user',
    'degree',
    'course',
    'thread'
);

-- Create notifications table
CREATE TABLE IF NOT EXISTS notifications (
    id BIGSERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type notification_type NOT NULL DEFAULT 'info',
    priority notification_priority NOT NULL DEFAULT 'normal',
    target_type target_type NOT NULL DEFAULT 'all',
    target_value VARCHAR(255), -- role name, user_id, degree_id, etc.
    sender_id BIGINT REFERENCES users(id) ON DELETE SET NULL,
    send_email BOOLEAN NOT NULL DEFAULT FALSE,
    email_subject VARCHAR(255),
    email_template TEXT,
    scheduled_at TIMESTAMP,
    sent_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create notification recipients table (for tracking who received what)
CREATE TABLE IF NOT EXISTS notification_recipients (
    id BIGSERIAL PRIMARY KEY,
    notification_id BIGINT NOT NULL REFERENCES notifications(id) ON DELETE CASCADE,
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    is_read BOOLEAN NOT NULL DEFAULT FALSE,
    read_at TIMESTAMP,
    email_sent BOOLEAN NOT NULL DEFAULT FALSE,
    email_sent_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(notification_id, user_id)
);

-- Create email templates table
CREATE TABLE IF NOT EXISTS email_templates (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    subject VARCHAR(255) NOT NULL,
    html_content TEXT NOT NULL,
    text_content TEXT,
    variables JSONB, -- Available template variables
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create triggers for updated_at
CREATE TRIGGER trg_notifications_updated
    BEFORE UPDATE ON notifications
    FOR EACH ROW
    EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER trg_notification_recipients_updated
    BEFORE UPDATE ON notification_recipients
    FOR EACH ROW
    EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER trg_email_templates_updated
    BEFORE UPDATE ON email_templates
    FOR EACH ROW
    EXECUTE FUNCTION update_timestamp();

-- Create indexes for performance
CREATE INDEX idx_notifications_target_type ON notifications(target_type);
CREATE INDEX idx_notifications_target_value ON notifications(target_value);
CREATE INDEX idx_notifications_sender_id ON notifications(sender_id);
CREATE INDEX idx_notifications_scheduled_at ON notifications(scheduled_at);
CREATE INDEX idx_notifications_sent_at ON notifications(sent_at);

CREATE INDEX idx_notification_recipients_user_id ON notification_recipients(user_id);
CREATE INDEX idx_notification_recipients_notification_id ON notification_recipients(notification_id);
CREATE INDEX idx_notification_recipients_is_read ON notification_recipients(is_read);

-- Insert default email templates
INSERT INTO email_templates (name, subject, html_content, text_content, variables) VALUES
('default_notification', 'Новое уведомление - {{title}}', 
 '<html><body><h2>{{title}}</h2><p>{{message}}</p><p>С уважением,<br>Команда EduNite</p></body></html>',
 '{{title}}\n\n{{message}}\n\nС уважением,\nКоманда EduNite',
 '{"title": "Заголовок уведомления", "message": "Текст сообщения"}'::jsonb),

('announcement', 'Объявление - {{title}}',
 '<html><body><div style="background-color: #f8f9fa; padding: 20px;"><h1 style="color: #007bff;">{{title}}</h1><p style="font-size: 16px;">{{message}}</p><hr><p style="color: #6c757d;">Это важное объявление от администрации EduNite</p></div></body></html>',
 'ОБЪЯВЛЕНИЕ: {{title}}\n\n{{message}}\n\nЭто важное объявление от администрации EduNite',
 '{"title": "Заголовок объявления", "message": "Текст объявления"}'::jsonb),

('urgent_notification', 'СРОЧНО: {{title}}',
 '<html><body><div style="border: 2px solid #dc3545; padding: 20px; background-color: #f8d7da;"><h1 style="color: #721c24;">СРОЧНОЕ УВЕДОМЛЕНИЕ</h1><h2>{{title}}</h2><p style="font-size: 16px; font-weight: bold;">{{message}}</p><p style="color: #721c24;">Требуется немедленное внимание!</p></div></body></html>',
 'СРОЧНОЕ УВЕДОМЛЕНИЕ: {{title}}\n\n{{message}}\n\nТребуется немедленное внимание!',
 '{"title": "Заголовок срочного уведомления", "message": "Текст срочного сообщения"}'::jsonb);
