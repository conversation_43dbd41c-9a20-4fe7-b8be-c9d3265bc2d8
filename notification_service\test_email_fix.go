package main

import (
	"context"
	"fmt"
	"log"

	"github.com/jackc/pgx/v4/pgxpool"
	"github.com/olzzhas/edunite-server/notification_service/internal/config"
	"github.com/olzzhas/edunite-server/notification_service/internal/email"
	"github.com/olzzhas/edunite-server/notification_service/internal/models"
	"github.com/olzzhas/edunite-server/notification_service/internal/repository"
)

func main() {
	// Load configuration
	cfg := config.LoadConfig()

	// Connect to database
	pool, err := pgxpool.Connect(context.Background(), cfg.Database.URL)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer pool.Close()

	// Initialize repository
	repo := repository.NewNotificationRepository(pool)

	// Initialize email service
	emailService := email.NewEmailService(cfg)

	// Test: Get default_notification template
	fmt.Println("Testing email template processing...")

	template, err := repo.GetEmailTemplate(context.Background(), "default_notification")
	if err != nil {
		log.Fatalf("Failed to get template: %v", err)
	}

	fmt.Printf("Template found: %s\n", template.Name)
	fmt.Printf("Subject: %s\n", template.Subject)
	fmt.Printf("HTML Content length: %d\n", len(template.HTMLContent))

	// Create test notification
	notification := &models.Notification{
		Title:         "Тестовое уведомление",
		Message:       "Это тест исправления email шаблонов",
		EmailTemplate: stringPtr("default_notification"),
	}

	// Create test user
	user := &models.User{
		ID:      1,
		Name:    "Тест",
		Surname: "Пользователь",
		Email:   "<EMAIL>",
	}

	// Test email sending (this will fail because of SMTP, but we can see the processing)
	fmt.Println("\nTesting email processing...")
	err = emailService.SendNotificationEmail(notification, user, template)
	if err != nil {
		fmt.Printf("Expected SMTP error (this is normal): %v\n", err)
	} else {
		fmt.Println("Email would be sent successfully!")
	}

	fmt.Println("\n✅ Test completed! The fix should work correctly.")
}

func stringPtr(s string) *string {
	return &s
}
