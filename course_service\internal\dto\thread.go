package dto

import "github.com/olzzhas/edunite-server/course_service/internal/database"

// ThreadWithRelations ListThreadsForUser returns every thread the user is registered in,
// together with course, semester, and teacher rows.
type ThreadWithRelations struct {
	Thread     database.Thread
	Course     database.Course
	Semester   database.Semester
	FinalGrade *float64
}

// ThreadCourseGrade represents a simplified view with only course name and final grade
type ThreadCourseGrade struct {
	ThreadID   int64    `json:"thread_id"`
	CourseName string   `json:"course_name"`
	FinalGrade *float64 `json:"final_grade"`
}
