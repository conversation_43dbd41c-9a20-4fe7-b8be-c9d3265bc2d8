package dto

import "time"

// AssignmentWithSubmissionDTO — одна строка из запроса
type AssignmentWithSubmissionDTO struct {
	// поля из assignments
	AssID             int64
	WeekID            int64
	Title             string
	Description       *string
	DueDate           *time.Time
	MaxPoints         *int32
	AssignmentGroupID *int64
	Type              string
	AssCreatedAt      time.Time
	AssUpdatedAt      time.Time

	// поля из submission (может быть NULL)
	SubID        *int64
	UserID       *int64
	SubmittedAt  *time.Time
	FileURLs     []string
	Comment      *string
	Score        *int32
	Feedback     *string
	SubCreatedAt *time.Time
	SubUpdatedAt *time.Time
}

// AssignmentInfoDTO — краткая информация о задании для журнала оценок
type AssignmentInfoDTO struct {
	ID        int64  `json:"id"`
	Title     string `json:"title"`
	MaxPoints int32  `json:"max_points"`
}

// GradeDTO — оценка студента за конкретное задание
type GradeDTO struct {
	AssignmentID int64  `json:"assignment_id"`
	Score        *int32 `json:"score"` // nil если оценки нет
}

// StudentGradesDTO — студент с его оценками
type StudentGradesDTO struct {
	ID         int64       `json:"id"`
	Name       string      `json:"name"`
	Surname    string      `json:"surname"`
	Grades     []*GradeDTO `json:"grades"`
	FinalGrade *float64    `json:"final_grade"` // nil если финальной оценки нет
}

// ThreadGradebookDTO — журнал оценок потока
type ThreadGradebookDTO struct {
	Assignments []*AssignmentInfoDTO `json:"assignments"`
	Students    []*StudentGradesDTO  `json:"students"`
}
