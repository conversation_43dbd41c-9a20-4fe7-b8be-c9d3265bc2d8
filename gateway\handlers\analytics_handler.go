package handlers

import (
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/olzzhas/edunite-server/gateway/clients"
)

// AnalyticsHandler handles HTTP requests for analytics
type AnalyticsHandler struct {
	AnalyticsService *clients.AnalyticsClient
}

// NewAnalyticsHandler creates a new analytics handler
func NewAnalyticsHandler(analyticsService *clients.AnalyticsClient) *AnalyticsHandler {
	return &AnalyticsHandler{
		AnalyticsService: analyticsService,
	}
}

// GetOverview handles GET /api/analytics/overview
func (h *AnalyticsHandler) GetOverview(c *gin.Context) {
	period := c.<PERSON><PERSON>ult<PERSON>("period", "last_30_days")

	response, err := h.AnalyticsService.GetOverview(c.Request.Context(), period)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": gin.H{
				"code":    "ANALYTICS_ERROR",
				"message": "Unable to fetch overview data",
				"details": err.Error(),
			},
		})
		return
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"stats": gin.H{
			"totalUsers": gin.H{
				"value":               response.TotalUsers.Value,
				"trend":               response.TotalUsers.Trend,
				"trendValue":          response.TotalUsers.TrendValue,
				"previousPeriodValue": response.TotalUsers.PreviousPeriodValue,
			},
			"totalCourses": gin.H{
				"value":               response.TotalCourses.Value,
				"trend":               response.TotalCourses.Trend,
				"trendValue":          response.TotalCourses.TrendValue,
				"previousPeriodValue": response.TotalCourses.PreviousPeriodValue,
			},
			"totalThreads": gin.H{
				"value":               response.TotalThreads.Value,
				"trend":               response.TotalThreads.Trend,
				"trendValue":          response.TotalThreads.TrendValue,
				"previousPeriodValue": response.TotalThreads.PreviousPeriodValue,
			},
			"activeStudents": gin.H{
				"value":               response.ActiveStudents.Value,
				"trend":               response.ActiveStudents.Trend,
				"trendValue":          response.ActiveStudents.TrendValue,
				"previousPeriodValue": response.ActiveStudents.PreviousPeriodValue,
			},
		},
		"period": response.Period,
	})
}

// GetUsersTimeline handles GET /api/analytics/users-timeline
func (h *AnalyticsHandler) GetUsersTimeline(c *gin.Context) {
	period := c.DefaultQuery("period", "30d")
	granularity := c.DefaultQuery("granularity", "day")

	response, err := h.AnalyticsService.GetUsersTimeline(c.Request.Context(), period, granularity)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": gin.H{
				"code":    "ANALYTICS_ERROR",
				"message": "Unable to fetch users timeline",
				"details": err.Error(),
			},
		})
		return
	}

	// Convert timeline points
	var timeline []gin.H
	for _, point := range response.Timeline {
		timeline = append(timeline, gin.H{
			"date":        point.Date,
			"totalUsers":  point.TotalUsers,
			"newUsers":    point.NewUsers,
			"activeUsers": point.ActiveUsers,
		})
	}

	// Convert comparison points
	var comparison []gin.H
	for _, point := range response.Comparison {
		comparison = append(comparison, gin.H{
			"date":        point.Date,
			"totalUsers":  point.TotalUsers,
			"newUsers":    point.NewUsers,
			"activeUsers": point.ActiveUsers,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"timeline":   timeline,
		"comparison": comparison,
	})
}

// GetUserDemographics handles GET /api/analytics/user-demographics
func (h *AnalyticsHandler) GetUserDemographics(c *gin.Context) {
	response, err := h.AnalyticsService.GetUserDemographics(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": gin.H{
				"code":    "ANALYTICS_ERROR",
				"message": "Unable to fetch user demographics",
				"details": err.Error(),
			},
		})
		return
	}

	// Convert devices
	var devices []gin.H
	for _, device := range response.Devices {
		devices = append(devices, gin.H{
			"name":       device.Name,
			"value":      device.Value,
			"percentage": device.Percentage,
		})
	}

	// Convert locations
	var locations []gin.H
	for _, location := range response.Locations {
		locations = append(locations, gin.H{
			"name":       location.Name,
			"value":      location.Value,
			"percentage": location.Percentage,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"devices":   devices,
		"locations": locations,
	})
}

// GetRecentActivities handles GET /api/analytics/recent-activities
func (h *AnalyticsHandler) GetRecentActivities(c *gin.Context) {
	limitStr := c.DefaultQuery("limit", "10")
	activityType := c.DefaultQuery("type", "all")

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": gin.H{
				"code":    "INVALID_PARAMETER",
				"message": "Invalid limit parameter",
				"details": "Limit must be a valid integer",
			},
		})
		return
	}

	response, err := h.AnalyticsService.GetRecentActivities(c.Request.Context(), int32(limit), activityType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": gin.H{
				"code":    "ANALYTICS_ERROR",
				"message": "Unable to fetch recent activities",
				"details": err.Error(),
			},
		})
		return
	}

	// Convert activities
	var activities []gin.H
	for _, activity := range response.Activities {
		activityData := gin.H{
			"id":   activity.Id,
			"type": activity.Type,
			"user": gin.H{
				"id":    activity.User.Id,
				"name":  activity.User.Name,
				"email": activity.User.Email,
			},
			"date":   activity.Date.AsTime().Format("2006-01-02T15:04:05Z"),
			"status": activity.Status,
		}

		// Add course info if available
		if activity.Course != nil {
			activityData["course"] = gin.H{
				"id":    activity.Course.Id,
				"title": activity.Course.Title,
			}
		}

		// Add assignment info if available
		if activity.Assignment != nil {
			activityData["assignment"] = gin.H{
				"id":    activity.Assignment.Id,
				"title": activity.Assignment.Title,
			}
		}

		activities = append(activities, activityData)
	}

	c.JSON(http.StatusOK, gin.H{
		"activities": activities,
	})
}

// GetDetailedStats handles GET /api/analytics/detailed-stats
func (h *AnalyticsHandler) GetDetailedStats(c *gin.Context) {
	period := c.DefaultQuery("period", "30d")
	metricsParam := c.DefaultQuery("metrics", "")

	var metrics []string
	if metricsParam != "" {
		metrics = strings.Split(metricsParam, ",")
	}

	response, err := h.AnalyticsService.GetDetailedStats(c.Request.Context(), period, metrics)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": gin.H{
				"code":    "ANALYTICS_ERROR",
				"message": "Unable to fetch detailed stats",
				"details": err.Error(),
			},
		})
		return
	}

	// Convert course enrollments timeline
	var enrollmentTimeline []gin.H
	for _, point := range response.CourseEnrollments.Timeline {
		enrollmentTimeline = append(enrollmentTimeline, gin.H{
			"date":        point.Date,
			"enrollments": point.Enrollments,
			"completions": point.Completions,
		})
	}

	// Convert course enrollments comparison
	var enrollmentComparison []gin.H
	for _, point := range response.CourseEnrollments.Comparison {
		enrollmentComparison = append(enrollmentComparison, gin.H{
			"date":        point.Date,
			"enrollments": point.Enrollments,
			"completions": point.Completions,
		})
	}

	// Convert user activity daily
	var userActivityDaily []gin.H
	for _, day := range response.UserActivity.Daily {
		userActivityDaily = append(userActivityDaily, gin.H{
			"day":         day.Day,
			"activeUsers": day.ActiveUsers,
		})
	}

	// Convert traffic sources
	var trafficSources []gin.H
	for _, source := range response.TrafficSources {
		trafficSources = append(trafficSources, gin.H{
			"source":     source.Source,
			"value":      source.Value,
			"percentage": source.Percentage,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"courseEnrollments": gin.H{
			"timeline":   enrollmentTimeline,
			"comparison": enrollmentComparison,
		},
		"userActivity": gin.H{
			"daily": userActivityDaily,
		},
		"trafficSources": trafficSources,
	})
}

// GetPerformanceMetrics handles GET /api/analytics/performance-metrics
func (h *AnalyticsHandler) GetPerformanceMetrics(c *gin.Context) {
	response, err := h.AnalyticsService.GetPerformanceMetrics(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": gin.H{
				"code":    "ANALYTICS_ERROR",
				"message": "Unable to fetch performance metrics",
				"details": err.Error(),
			},
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"metrics": gin.H{
			"courseCompletionRate": gin.H{
				"value":       response.CourseCompletionRate.Value,
				"unit":        response.CourseCompletionRate.Unit,
				"trend":       response.CourseCompletionRate.Trend,
				"change":      response.CourseCompletionRate.Change,
				"description": response.CourseCompletionRate.Description,
			},
			"averageGrade": gin.H{
				"value":       response.AverageGrade.Value,
				"unit":        response.AverageGrade.Unit,
				"trend":       response.AverageGrade.Trend,
				"change":      response.AverageGrade.Change,
				"description": response.AverageGrade.Description,
			},
			"attendanceRate": gin.H{
				"value":       response.AttendanceRate.Value,
				"unit":        response.AttendanceRate.Unit,
				"trend":       response.AttendanceRate.Trend,
				"change":      response.AttendanceRate.Change,
				"description": response.AttendanceRate.Description,
			},
			"averageSessionDuration": gin.H{
				"value":       "2m 45s", // Use string value for session duration
				"unit":        response.AverageSessionDuration.Unit,
				"trend":       response.AverageSessionDuration.Trend,
				"change":      response.AverageSessionDuration.Change,
				"description": response.AverageSessionDuration.Description,
			},
		},
	})
}
