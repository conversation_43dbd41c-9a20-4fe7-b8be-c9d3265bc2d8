syntax = "proto3";

package attendancepb;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/olzzhas/edunite-server/course_service/pb/attendance;attendancepb";

// AttendanceService manages attendance records for threads
service AttendanceService {
  rpc CreateAttendance (AttendanceRequest) returns (AttendanceResponse) {};
  rpc ListAttendance (ListAttendanceRequest) returns (ListAttendanceResponse) {};
  rpc UpdateAttendance (UpdateAttendanceRequest) returns (AttendanceResponse) {};
  rpc DeleteAttendanceByID (AttendanceIDRequest) returns (AttendanceEmptyResponse) {};
  rpc DeleteAttendancesByThreadUser (AttendancesByThreadUser) returns (AttendanceEmptyResponse) {}
}

// Attendance status enumeration
enum AttendanceStatus {
  ATTENDANCE_STATUS_UNSPECIFIED = 0;
  ATTENDANCE_STATUS_UNMARKED     = 1;
  ATTENDANCE_STATUS_PRESENT      = 2;
  ATTENDANCE_STATUS_ABSENT       = 3;
  ATTENDANCE_STATUS_EXCUSED      = 4;
}

// Request to create or update attendance
message AttendanceRequest {
  int64 thread_id       = 1;
  int64 user_id         = 2;
  google.protobuf.Timestamp attendance_date = 3;
  AttendanceStatus status = 4;
  string reason           = 5; // optional if status is EXCUSED
}

// User information for attendance records
message AttendanceUser {
  int64 id = 1;
  string name = 2;
  string surname = 3;
  string email = 4;
  string role = 5;
}

// Response with attendance record details
message AttendanceResponse {
  int64 id             = 1;
  int64 thread_id      = 2;
  int64 user_id        = 3;
  google.protobuf.Timestamp attendance_date = 4;
  AttendanceStatus status = 5;
  string reason           = 6;
  google.protobuf.Timestamp created_at  = 7;
  google.protobuf.Timestamp updated_at  = 8;
}

// Response with attendance record details including user information
message AttendanceWithUserResponse {
  int64 id             = 1;
  int64 thread_id      = 2;
  int64 user_id        = 3;
  google.protobuf.Timestamp attendance_date = 4;
  AttendanceStatus status = 5;
  string reason           = 6;
  google.protobuf.Timestamp created_at  = 7;
  google.protobuf.Timestamp updated_at  = 8;
  AttendanceUser user  = 9;
}

// Request to list attendance by thread and date
message ListAttendanceRequest {
  int64 thread_id = 1;
  google.protobuf.Timestamp attendance_date = 2;
}

// Response containing multiple attendance records
message ListAttendanceResponse {
  repeated AttendanceWithUserResponse records = 1;
}

// Request to update status or reason
message UpdateAttendanceRequest {
  int64 id                       = 1;
  AttendanceStatus status       = 2;
  string reason                 = 3;
}

// Request to delete attendance record by ID
message AttendanceIDRequest {
  int64 id = 1;
}

message AttendancesByThreadUser {
  int64 thread_id = 1;
  int64 user_id = 2;
}

// Empty response for delete operations
message AttendanceEmptyResponse {}
