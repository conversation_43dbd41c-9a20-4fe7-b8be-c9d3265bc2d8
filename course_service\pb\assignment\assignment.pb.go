// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.28.2
// source: pb/assignment/assignment.proto

package assignmentpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// -------------------------------//
//
//	Assignment Attachment        //
//
// -------------------------------//
type AssignmentAttachmentResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	AssignmentId  int64                  `protobuf:"varint,2,opt,name=assignment_id,json=assignmentId,proto3" json:"assignment_id,omitempty"`
	FileUrl       string                 `protobuf:"bytes,3,opt,name=file_url,json=fileUrl,proto3" json:"file_url,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssignmentAttachmentResponse) Reset() {
	*x = AssignmentAttachmentResponse{}
	mi := &file_pb_assignment_assignment_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssignmentAttachmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignmentAttachmentResponse) ProtoMessage() {}

func (x *AssignmentAttachmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_assignment_assignment_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignmentAttachmentResponse.ProtoReflect.Descriptor instead.
func (*AssignmentAttachmentResponse) Descriptor() ([]byte, []int) {
	return file_pb_assignment_assignment_proto_rawDescGZIP(), []int{0}
}

func (x *AssignmentAttachmentResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AssignmentAttachmentResponse) GetAssignmentId() int64 {
	if x != nil {
		return x.AssignmentId
	}
	return 0
}

func (x *AssignmentAttachmentResponse) GetFileUrl() string {
	if x != nil {
		return x.FileUrl
	}
	return ""
}

func (x *AssignmentAttachmentResponse) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *AssignmentAttachmentResponse) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type AssignmentAttachmentsResponse struct {
	state         protoimpl.MessageState          `protogen:"open.v1"`
	Attachments   []*AssignmentAttachmentResponse `protobuf:"bytes,1,rep,name=attachments,proto3" json:"attachments,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssignmentAttachmentsResponse) Reset() {
	*x = AssignmentAttachmentsResponse{}
	mi := &file_pb_assignment_assignment_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssignmentAttachmentsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignmentAttachmentsResponse) ProtoMessage() {}

func (x *AssignmentAttachmentsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_assignment_assignment_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignmentAttachmentsResponse.ProtoReflect.Descriptor instead.
func (*AssignmentAttachmentsResponse) Descriptor() ([]byte, []int) {
	return file_pb_assignment_assignment_proto_rawDescGZIP(), []int{1}
}

func (x *AssignmentAttachmentsResponse) GetAttachments() []*AssignmentAttachmentResponse {
	if x != nil {
		return x.Attachments
	}
	return nil
}

// CreateAttachment
type CreateAssignmentAttachmentRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AssignmentId  int64                  `protobuf:"varint,1,opt,name=assignment_id,json=assignmentId,proto3" json:"assignment_id,omitempty"`
	FileUrl       string                 `protobuf:"bytes,2,opt,name=file_url,json=fileUrl,proto3" json:"file_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateAssignmentAttachmentRequest) Reset() {
	*x = CreateAssignmentAttachmentRequest{}
	mi := &file_pb_assignment_assignment_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAssignmentAttachmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAssignmentAttachmentRequest) ProtoMessage() {}

func (x *CreateAssignmentAttachmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_assignment_assignment_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAssignmentAttachmentRequest.ProtoReflect.Descriptor instead.
func (*CreateAssignmentAttachmentRequest) Descriptor() ([]byte, []int) {
	return file_pb_assignment_assignment_proto_rawDescGZIP(), []int{2}
}

func (x *CreateAssignmentAttachmentRequest) GetAssignmentId() int64 {
	if x != nil {
		return x.AssignmentId
	}
	return 0
}

func (x *CreateAssignmentAttachmentRequest) GetFileUrl() string {
	if x != nil {
		return x.FileUrl
	}
	return ""
}

// For deletion
type AssignmentAttachmentByIDRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AttachmentId  int64                  `protobuf:"varint,1,opt,name=attachment_id,json=attachmentId,proto3" json:"attachment_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssignmentAttachmentByIDRequest) Reset() {
	*x = AssignmentAttachmentByIDRequest{}
	mi := &file_pb_assignment_assignment_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssignmentAttachmentByIDRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignmentAttachmentByIDRequest) ProtoMessage() {}

func (x *AssignmentAttachmentByIDRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_assignment_assignment_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignmentAttachmentByIDRequest.ProtoReflect.Descriptor instead.
func (*AssignmentAttachmentByIDRequest) Descriptor() ([]byte, []int) {
	return file_pb_assignment_assignment_proto_rawDescGZIP(), []int{3}
}

func (x *AssignmentAttachmentByIDRequest) GetAttachmentId() int64 {
	if x != nil {
		return x.AttachmentId
	}
	return 0
}

// For listing
type AssignmentIDRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AssignmentId  int64                  `protobuf:"varint,1,opt,name=assignment_id,json=assignmentId,proto3" json:"assignment_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssignmentIDRequest) Reset() {
	*x = AssignmentIDRequest{}
	mi := &file_pb_assignment_assignment_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssignmentIDRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignmentIDRequest) ProtoMessage() {}

func (x *AssignmentIDRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_assignment_assignment_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignmentIDRequest.ProtoReflect.Descriptor instead.
func (*AssignmentIDRequest) Descriptor() ([]byte, []int) {
	return file_pb_assignment_assignment_proto_rawDescGZIP(), []int{4}
}

func (x *AssignmentIDRequest) GetAssignmentId() int64 {
	if x != nil {
		return x.AssignmentId
	}
	return 0
}

// -------------------------------//
//
//	Assignment Submission        //
//
// -------------------------------//
type AssignmentSubmissionResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	AssignmentId  int64                  `protobuf:"varint,2,opt,name=assignment_id,json=assignmentId,proto3" json:"assignment_id,omitempty"`
	UserId        int64                  `protobuf:"varint,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	SubmittedAt   *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=submitted_at,json=submittedAt,proto3" json:"submitted_at,omitempty"`
	FileUrls      []string               `protobuf:"bytes,5,rep,name=file_urls,json=fileUrls,proto3" json:"file_urls,omitempty"`
	Comment       string                 `protobuf:"bytes,6,opt,name=comment,proto3" json:"comment,omitempty"`
	Score         int32                  `protobuf:"varint,7,opt,name=score,proto3" json:"score,omitempty"`
	Feedback      string                 `protobuf:"bytes,8,opt,name=feedback,proto3" json:"feedback,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssignmentSubmissionResponse) Reset() {
	*x = AssignmentSubmissionResponse{}
	mi := &file_pb_assignment_assignment_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssignmentSubmissionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignmentSubmissionResponse) ProtoMessage() {}

func (x *AssignmentSubmissionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_assignment_assignment_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignmentSubmissionResponse.ProtoReflect.Descriptor instead.
func (*AssignmentSubmissionResponse) Descriptor() ([]byte, []int) {
	return file_pb_assignment_assignment_proto_rawDescGZIP(), []int{5}
}

func (x *AssignmentSubmissionResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AssignmentSubmissionResponse) GetAssignmentId() int64 {
	if x != nil {
		return x.AssignmentId
	}
	return 0
}

func (x *AssignmentSubmissionResponse) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *AssignmentSubmissionResponse) GetSubmittedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.SubmittedAt
	}
	return nil
}

func (x *AssignmentSubmissionResponse) GetFileUrls() []string {
	if x != nil {
		return x.FileUrls
	}
	return nil
}

func (x *AssignmentSubmissionResponse) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

func (x *AssignmentSubmissionResponse) GetScore() int32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *AssignmentSubmissionResponse) GetFeedback() string {
	if x != nil {
		return x.Feedback
	}
	return ""
}

func (x *AssignmentSubmissionResponse) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *AssignmentSubmissionResponse) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type AssignmentSubmissionsResponse struct {
	state         protoimpl.MessageState          `protogen:"open.v1"`
	Submissions   []*AssignmentSubmissionResponse `protobuf:"bytes,1,rep,name=submissions,proto3" json:"submissions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssignmentSubmissionsResponse) Reset() {
	*x = AssignmentSubmissionsResponse{}
	mi := &file_pb_assignment_assignment_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssignmentSubmissionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignmentSubmissionsResponse) ProtoMessage() {}

func (x *AssignmentSubmissionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_assignment_assignment_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignmentSubmissionsResponse.ProtoReflect.Descriptor instead.
func (*AssignmentSubmissionsResponse) Descriptor() ([]byte, []int) {
	return file_pb_assignment_assignment_proto_rawDescGZIP(), []int{6}
}

func (x *AssignmentSubmissionsResponse) GetSubmissions() []*AssignmentSubmissionResponse {
	if x != nil {
		return x.Submissions
	}
	return nil
}

// Создать новую сдачу
type CreateAssignmentSubmissionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AssignmentId  int64                  `protobuf:"varint,1,opt,name=assignment_id,json=assignmentId,proto3" json:"assignment_id,omitempty"`
	UserId        int64                  `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	FileUrls      []string               `protobuf:"bytes,3,rep,name=file_urls,json=fileUrls,proto3" json:"file_urls,omitempty"`
	Comment       string                 `protobuf:"bytes,4,opt,name=comment,proto3" json:"comment,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateAssignmentSubmissionRequest) Reset() {
	*x = CreateAssignmentSubmissionRequest{}
	mi := &file_pb_assignment_assignment_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAssignmentSubmissionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAssignmentSubmissionRequest) ProtoMessage() {}

func (x *CreateAssignmentSubmissionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_assignment_assignment_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAssignmentSubmissionRequest.ProtoReflect.Descriptor instead.
func (*CreateAssignmentSubmissionRequest) Descriptor() ([]byte, []int) {
	return file_pb_assignment_assignment_proto_rawDescGZIP(), []int{7}
}

func (x *CreateAssignmentSubmissionRequest) GetAssignmentId() int64 {
	if x != nil {
		return x.AssignmentId
	}
	return 0
}

func (x *CreateAssignmentSubmissionRequest) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *CreateAssignmentSubmissionRequest) GetFileUrls() []string {
	if x != nil {
		return x.FileUrls
	}
	return nil
}

func (x *CreateAssignmentSubmissionRequest) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

// Получить сабмит
type AssignmentSubmissionByIDRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SubmissionId  int64                  `protobuf:"varint,1,opt,name=submission_id,json=submissionId,proto3" json:"submission_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssignmentSubmissionByIDRequest) Reset() {
	*x = AssignmentSubmissionByIDRequest{}
	mi := &file_pb_assignment_assignment_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssignmentSubmissionByIDRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignmentSubmissionByIDRequest) ProtoMessage() {}

func (x *AssignmentSubmissionByIDRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_assignment_assignment_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignmentSubmissionByIDRequest.ProtoReflect.Descriptor instead.
func (*AssignmentSubmissionByIDRequest) Descriptor() ([]byte, []int) {
	return file_pb_assignment_assignment_proto_rawDescGZIP(), []int{8}
}

func (x *AssignmentSubmissionByIDRequest) GetSubmissionId() int64 {
	if x != nil {
		return x.SubmissionId
	}
	return 0
}

// Обновить оценку
type UpdateAssignmentSubmissionScoreRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SubmissionId  int64                  `protobuf:"varint,1,opt,name=submission_id,json=submissionId,proto3" json:"submission_id,omitempty"`
	Score         int32                  `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`
	Feedback      string                 `protobuf:"bytes,3,opt,name=feedback,proto3" json:"feedback,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateAssignmentSubmissionScoreRequest) Reset() {
	*x = UpdateAssignmentSubmissionScoreRequest{}
	mi := &file_pb_assignment_assignment_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAssignmentSubmissionScoreRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAssignmentSubmissionScoreRequest) ProtoMessage() {}

func (x *UpdateAssignmentSubmissionScoreRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_assignment_assignment_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAssignmentSubmissionScoreRequest.ProtoReflect.Descriptor instead.
func (*UpdateAssignmentSubmissionScoreRequest) Descriptor() ([]byte, []int) {
	return file_pb_assignment_assignment_proto_rawDescGZIP(), []int{9}
}

func (x *UpdateAssignmentSubmissionScoreRequest) GetSubmissionId() int64 {
	if x != nil {
		return x.SubmissionId
	}
	return 0
}

func (x *UpdateAssignmentSubmissionScoreRequest) GetScore() int32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *UpdateAssignmentSubmissionScoreRequest) GetFeedback() string {
	if x != nil {
		return x.Feedback
	}
	return ""
}

// -------------------------------//
//
//	Assignment Group Messages    //
//
// -------------------------------//
type AssignmentGroupResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ThreadId      int64                  `protobuf:"varint,2,opt,name=thread_id,json=threadId,proto3" json:"thread_id,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	GroupType     string                 `protobuf:"bytes,4,opt,name=group_type,json=groupType,proto3" json:"group_type,omitempty"`
	Weight        float32                `protobuf:"fixed32,5,opt,name=weight,proto3" json:"weight,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssignmentGroupResponse) Reset() {
	*x = AssignmentGroupResponse{}
	mi := &file_pb_assignment_assignment_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssignmentGroupResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignmentGroupResponse) ProtoMessage() {}

func (x *AssignmentGroupResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_assignment_assignment_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignmentGroupResponse.ProtoReflect.Descriptor instead.
func (*AssignmentGroupResponse) Descriptor() ([]byte, []int) {
	return file_pb_assignment_assignment_proto_rawDescGZIP(), []int{10}
}

func (x *AssignmentGroupResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AssignmentGroupResponse) GetThreadId() int64 {
	if x != nil {
		return x.ThreadId
	}
	return 0
}

func (x *AssignmentGroupResponse) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AssignmentGroupResponse) GetGroupType() string {
	if x != nil {
		return x.GroupType
	}
	return ""
}

func (x *AssignmentGroupResponse) GetWeight() float32 {
	if x != nil {
		return x.Weight
	}
	return 0
}

func (x *AssignmentGroupResponse) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *AssignmentGroupResponse) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type AssignmentGroupsResponse struct {
	state            protoimpl.MessageState     `protogen:"open.v1"`
	AssignmentGroups []*AssignmentGroupResponse `protobuf:"bytes,1,rep,name=assignment_groups,json=assignmentGroups,proto3" json:"assignment_groups,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *AssignmentGroupsResponse) Reset() {
	*x = AssignmentGroupsResponse{}
	mi := &file_pb_assignment_assignment_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssignmentGroupsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignmentGroupsResponse) ProtoMessage() {}

func (x *AssignmentGroupsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_assignment_assignment_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignmentGroupsResponse.ProtoReflect.Descriptor instead.
func (*AssignmentGroupsResponse) Descriptor() ([]byte, []int) {
	return file_pb_assignment_assignment_proto_rawDescGZIP(), []int{11}
}

func (x *AssignmentGroupsResponse) GetAssignmentGroups() []*AssignmentGroupResponse {
	if x != nil {
		return x.AssignmentGroups
	}
	return nil
}

type AssignmentGroupByID struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AssignmentId  int64                  `protobuf:"varint,1,opt,name=assignment_id,json=assignmentId,proto3" json:"assignment_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssignmentGroupByID) Reset() {
	*x = AssignmentGroupByID{}
	mi := &file_pb_assignment_assignment_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssignmentGroupByID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignmentGroupByID) ProtoMessage() {}

func (x *AssignmentGroupByID) ProtoReflect() protoreflect.Message {
	mi := &file_pb_assignment_assignment_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignmentGroupByID.ProtoReflect.Descriptor instead.
func (*AssignmentGroupByID) Descriptor() ([]byte, []int) {
	return file_pb_assignment_assignment_proto_rawDescGZIP(), []int{12}
}

func (x *AssignmentGroupByID) GetAssignmentId() int64 {
	if x != nil {
		return x.AssignmentId
	}
	return 0
}

type AssignmentGroupRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ThreadId      int64                  `protobuf:"varint,1,opt,name=thread_id,json=threadId,proto3" json:"thread_id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	GroupType     string                 `protobuf:"bytes,3,opt,name=group_type,json=groupType,proto3" json:"group_type,omitempty"`
	Weight        float32                `protobuf:"fixed32,4,opt,name=weight,proto3" json:"weight,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssignmentGroupRequest) Reset() {
	*x = AssignmentGroupRequest{}
	mi := &file_pb_assignment_assignment_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssignmentGroupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignmentGroupRequest) ProtoMessage() {}

func (x *AssignmentGroupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_assignment_assignment_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignmentGroupRequest.ProtoReflect.Descriptor instead.
func (*AssignmentGroupRequest) Descriptor() ([]byte, []int) {
	return file_pb_assignment_assignment_proto_rawDescGZIP(), []int{13}
}

func (x *AssignmentGroupRequest) GetThreadId() int64 {
	if x != nil {
		return x.ThreadId
	}
	return 0
}

func (x *AssignmentGroupRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AssignmentGroupRequest) GetGroupType() string {
	if x != nil {
		return x.GroupType
	}
	return ""
}

func (x *AssignmentGroupRequest) GetWeight() float32 {
	if x != nil {
		return x.Weight
	}
	return 0
}

type AssignmentGroupUpdateRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ThreadId      int64                  `protobuf:"varint,2,opt,name=thread_id,json=threadId,proto3" json:"thread_id,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	GroupType     string                 `protobuf:"bytes,4,opt,name=group_type,json=groupType,proto3" json:"group_type,omitempty"`
	Weight        float32                `protobuf:"fixed32,5,opt,name=weight,proto3" json:"weight,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssignmentGroupUpdateRequest) Reset() {
	*x = AssignmentGroupUpdateRequest{}
	mi := &file_pb_assignment_assignment_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssignmentGroupUpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignmentGroupUpdateRequest) ProtoMessage() {}

func (x *AssignmentGroupUpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_assignment_assignment_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignmentGroupUpdateRequest.ProtoReflect.Descriptor instead.
func (*AssignmentGroupUpdateRequest) Descriptor() ([]byte, []int) {
	return file_pb_assignment_assignment_proto_rawDescGZIP(), []int{14}
}

func (x *AssignmentGroupUpdateRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AssignmentGroupUpdateRequest) GetThreadId() int64 {
	if x != nil {
		return x.ThreadId
	}
	return 0
}

func (x *AssignmentGroupUpdateRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AssignmentGroupUpdateRequest) GetGroupType() string {
	if x != nil {
		return x.GroupType
	}
	return ""
}

func (x *AssignmentGroupUpdateRequest) GetWeight() float32 {
	if x != nil {
		return x.Weight
	}
	return 0
}

type AssignmentGroupsForThread struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ThreadId      int64                  `protobuf:"varint,1,opt,name=thread_id,json=threadId,proto3" json:"thread_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssignmentGroupsForThread) Reset() {
	*x = AssignmentGroupsForThread{}
	mi := &file_pb_assignment_assignment_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssignmentGroupsForThread) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignmentGroupsForThread) ProtoMessage() {}

func (x *AssignmentGroupsForThread) ProtoReflect() protoreflect.Message {
	mi := &file_pb_assignment_assignment_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignmentGroupsForThread.ProtoReflect.Descriptor instead.
func (*AssignmentGroupsForThread) Descriptor() ([]byte, []int) {
	return file_pb_assignment_assignment_proto_rawDescGZIP(), []int{15}
}

func (x *AssignmentGroupsForThread) GetThreadId() int64 {
	if x != nil {
		return x.ThreadId
	}
	return 0
}

type AssignmentEmptyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssignmentEmptyResponse) Reset() {
	*x = AssignmentEmptyResponse{}
	mi := &file_pb_assignment_assignment_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssignmentEmptyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignmentEmptyResponse) ProtoMessage() {}

func (x *AssignmentEmptyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_assignment_assignment_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignmentEmptyResponse.ProtoReflect.Descriptor instead.
func (*AssignmentEmptyResponse) Descriptor() ([]byte, []int) {
	return file_pb_assignment_assignment_proto_rawDescGZIP(), []int{16}
}

type AssignmentEmptyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssignmentEmptyRequest) Reset() {
	*x = AssignmentEmptyRequest{}
	mi := &file_pb_assignment_assignment_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssignmentEmptyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignmentEmptyRequest) ProtoMessage() {}

func (x *AssignmentEmptyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_assignment_assignment_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignmentEmptyRequest.ProtoReflect.Descriptor instead.
func (*AssignmentEmptyRequest) Descriptor() ([]byte, []int) {
	return file_pb_assignment_assignment_proto_rawDescGZIP(), []int{17}
}

type AssignmentResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Id                int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	WeekId            int64                  `protobuf:"varint,2,opt,name=week_id,json=weekId,proto3" json:"week_id,omitempty"`
	Title             string                 `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	Description       string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	DueDate           *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=due_date,json=dueDate,proto3" json:"due_date,omitempty"`
	MaxPoints         int32                  `protobuf:"varint,6,opt,name=max_points,json=maxPoints,proto3" json:"max_points,omitempty"`
	AssignmentGroupId int64                  `protobuf:"varint,7,opt,name=assignment_group_id,json=assignmentGroupId,proto3" json:"assignment_group_id,omitempty"`
	Type              string                 `protobuf:"bytes,8,opt,name=type,proto3" json:"type,omitempty"`
	CreatedAt         *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt         *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *AssignmentResponse) Reset() {
	*x = AssignmentResponse{}
	mi := &file_pb_assignment_assignment_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssignmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignmentResponse) ProtoMessage() {}

func (x *AssignmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_assignment_assignment_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignmentResponse.ProtoReflect.Descriptor instead.
func (*AssignmentResponse) Descriptor() ([]byte, []int) {
	return file_pb_assignment_assignment_proto_rawDescGZIP(), []int{18}
}

func (x *AssignmentResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AssignmentResponse) GetWeekId() int64 {
	if x != nil {
		return x.WeekId
	}
	return 0
}

func (x *AssignmentResponse) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *AssignmentResponse) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *AssignmentResponse) GetDueDate() *timestamppb.Timestamp {
	if x != nil {
		return x.DueDate
	}
	return nil
}

func (x *AssignmentResponse) GetMaxPoints() int32 {
	if x != nil {
		return x.MaxPoints
	}
	return 0
}

func (x *AssignmentResponse) GetAssignmentGroupId() int64 {
	if x != nil {
		return x.AssignmentGroupId
	}
	return 0
}

func (x *AssignmentResponse) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *AssignmentResponse) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *AssignmentResponse) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

type AssignmentsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Assignments   []*AssignmentResponse  `protobuf:"bytes,1,rep,name=assignments,proto3" json:"assignments,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssignmentsResponse) Reset() {
	*x = AssignmentsResponse{}
	mi := &file_pb_assignment_assignment_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssignmentsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignmentsResponse) ProtoMessage() {}

func (x *AssignmentsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_assignment_assignment_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignmentsResponse.ProtoReflect.Descriptor instead.
func (*AssignmentsResponse) Descriptor() ([]byte, []int) {
	return file_pb_assignment_assignment_proto_rawDescGZIP(), []int{19}
}

func (x *AssignmentsResponse) GetAssignments() []*AssignmentResponse {
	if x != nil {
		return x.Assignments
	}
	return nil
}

type AssignmentRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	WeekId            int64                  `protobuf:"varint,1,opt,name=week_id,json=weekId,proto3" json:"week_id,omitempty"`
	Title             string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Description       string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	DueDate           *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=due_date,json=dueDate,proto3" json:"due_date,omitempty"`
	MaxPoints         int32                  `protobuf:"varint,5,opt,name=max_points,json=maxPoints,proto3" json:"max_points,omitempty"`
	AssignmentGroupId int64                  `protobuf:"varint,6,opt,name=assignment_group_id,json=assignmentGroupId,proto3" json:"assignment_group_id,omitempty"` // optional
	Type              string                 `protobuf:"bytes,7,opt,name=type,proto3" json:"type,omitempty"`                                                       // 'task' or 'info'
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *AssignmentRequest) Reset() {
	*x = AssignmentRequest{}
	mi := &file_pb_assignment_assignment_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssignmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignmentRequest) ProtoMessage() {}

func (x *AssignmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_assignment_assignment_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignmentRequest.ProtoReflect.Descriptor instead.
func (*AssignmentRequest) Descriptor() ([]byte, []int) {
	return file_pb_assignment_assignment_proto_rawDescGZIP(), []int{20}
}

func (x *AssignmentRequest) GetWeekId() int64 {
	if x != nil {
		return x.WeekId
	}
	return 0
}

func (x *AssignmentRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *AssignmentRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *AssignmentRequest) GetDueDate() *timestamppb.Timestamp {
	if x != nil {
		return x.DueDate
	}
	return nil
}

func (x *AssignmentRequest) GetMaxPoints() int32 {
	if x != nil {
		return x.MaxPoints
	}
	return 0
}

func (x *AssignmentRequest) GetAssignmentGroupId() int64 {
	if x != nil {
		return x.AssignmentGroupId
	}
	return 0
}

func (x *AssignmentRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

type AssignmentUpdateRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Id                int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	WeekId            int64                  `protobuf:"varint,2,opt,name=week_id,json=weekId,proto3" json:"week_id,omitempty"`
	Title             string                 `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	Description       string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	DueDate           *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=due_date,json=dueDate,proto3" json:"due_date,omitempty"`
	MaxPoints         int32                  `protobuf:"varint,6,opt,name=max_points,json=maxPoints,proto3" json:"max_points,omitempty"`
	AssignmentGroupId int64                  `protobuf:"varint,7,opt,name=assignment_group_id,json=assignmentGroupId,proto3" json:"assignment_group_id,omitempty"`
	Type              string                 `protobuf:"bytes,8,opt,name=type,proto3" json:"type,omitempty"` // 'task' or 'info'
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *AssignmentUpdateRequest) Reset() {
	*x = AssignmentUpdateRequest{}
	mi := &file_pb_assignment_assignment_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssignmentUpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignmentUpdateRequest) ProtoMessage() {}

func (x *AssignmentUpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_assignment_assignment_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignmentUpdateRequest.ProtoReflect.Descriptor instead.
func (*AssignmentUpdateRequest) Descriptor() ([]byte, []int) {
	return file_pb_assignment_assignment_proto_rawDescGZIP(), []int{21}
}

func (x *AssignmentUpdateRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AssignmentUpdateRequest) GetWeekId() int64 {
	if x != nil {
		return x.WeekId
	}
	return 0
}

func (x *AssignmentUpdateRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *AssignmentUpdateRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *AssignmentUpdateRequest) GetDueDate() *timestamppb.Timestamp {
	if x != nil {
		return x.DueDate
	}
	return nil
}

func (x *AssignmentUpdateRequest) GetMaxPoints() int32 {
	if x != nil {
		return x.MaxPoints
	}
	return 0
}

func (x *AssignmentUpdateRequest) GetAssignmentGroupId() int64 {
	if x != nil {
		return x.AssignmentGroupId
	}
	return 0
}

func (x *AssignmentUpdateRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

type AssignmentsForWeekRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	WeekId        int64                  `protobuf:"varint,1,opt,name=week_id,json=weekId,proto3" json:"week_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssignmentsForWeekRequest) Reset() {
	*x = AssignmentsForWeekRequest{}
	mi := &file_pb_assignment_assignment_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssignmentsForWeekRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignmentsForWeekRequest) ProtoMessage() {}

func (x *AssignmentsForWeekRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_assignment_assignment_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignmentsForWeekRequest.ProtoReflect.Descriptor instead.
func (*AssignmentsForWeekRequest) Descriptor() ([]byte, []int) {
	return file_pb_assignment_assignment_proto_rawDescGZIP(), []int{22}
}

func (x *AssignmentsForWeekRequest) GetWeekId() int64 {
	if x != nil {
		return x.WeekId
	}
	return 0
}

type AssignmentByID struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssignmentByID) Reset() {
	*x = AssignmentByID{}
	mi := &file_pb_assignment_assignment_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssignmentByID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignmentByID) ProtoMessage() {}

func (x *AssignmentByID) ProtoReflect() protoreflect.Message {
	mi := &file_pb_assignment_assignment_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignmentByID.ProtoReflect.Descriptor instead.
func (*AssignmentByID) Descriptor() ([]byte, []int) {
	return file_pb_assignment_assignment_proto_rawDescGZIP(), []int{23}
}

func (x *AssignmentByID) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type AssignmentsWithSubmissionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ThreadId      int64                  `protobuf:"varint,1,opt,name=thread_id,json=threadId,proto3" json:"thread_id,omitempty"`
	UserId        int64                  `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssignmentsWithSubmissionRequest) Reset() {
	*x = AssignmentsWithSubmissionRequest{}
	mi := &file_pb_assignment_assignment_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssignmentsWithSubmissionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignmentsWithSubmissionRequest) ProtoMessage() {}

func (x *AssignmentsWithSubmissionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_assignment_assignment_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignmentsWithSubmissionRequest.ProtoReflect.Descriptor instead.
func (*AssignmentsWithSubmissionRequest) Descriptor() ([]byte, []int) {
	return file_pb_assignment_assignment_proto_rawDescGZIP(), []int{24}
}

func (x *AssignmentsWithSubmissionRequest) GetThreadId() int64 {
	if x != nil {
		return x.ThreadId
	}
	return 0
}

func (x *AssignmentsWithSubmissionRequest) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type AssignmentWithSubmission struct {
	state         protoimpl.MessageState        `protogen:"open.v1"`
	Assignment    *AssignmentResponse           `protobuf:"bytes,1,opt,name=assignment,proto3" json:"assignment,omitempty"`
	Submission    *AssignmentSubmissionResponse `protobuf:"bytes,2,opt,name=submission,proto3" json:"submission,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssignmentWithSubmission) Reset() {
	*x = AssignmentWithSubmission{}
	mi := &file_pb_assignment_assignment_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssignmentWithSubmission) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignmentWithSubmission) ProtoMessage() {}

func (x *AssignmentWithSubmission) ProtoReflect() protoreflect.Message {
	mi := &file_pb_assignment_assignment_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignmentWithSubmission.ProtoReflect.Descriptor instead.
func (*AssignmentWithSubmission) Descriptor() ([]byte, []int) {
	return file_pb_assignment_assignment_proto_rawDescGZIP(), []int{25}
}

func (x *AssignmentWithSubmission) GetAssignment() *AssignmentResponse {
	if x != nil {
		return x.Assignment
	}
	return nil
}

func (x *AssignmentWithSubmission) GetSubmission() *AssignmentSubmissionResponse {
	if x != nil {
		return x.Submission
	}
	return nil
}

type AssignmentsWithSubmissionResponse struct {
	state         protoimpl.MessageState      `protogen:"open.v1"`
	Items         []*AssignmentWithSubmission `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssignmentsWithSubmissionResponse) Reset() {
	*x = AssignmentsWithSubmissionResponse{}
	mi := &file_pb_assignment_assignment_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssignmentsWithSubmissionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignmentsWithSubmissionResponse) ProtoMessage() {}

func (x *AssignmentsWithSubmissionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_assignment_assignment_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignmentsWithSubmissionResponse.ProtoReflect.Descriptor instead.
func (*AssignmentsWithSubmissionResponse) Descriptor() ([]byte, []int) {
	return file_pb_assignment_assignment_proto_rawDescGZIP(), []int{26}
}

func (x *AssignmentsWithSubmissionResponse) GetItems() []*AssignmentWithSubmission {
	if x != nil {
		return x.Items
	}
	return nil
}

// -------------------------------//
// Comprehensive Assignment Details //
// -------------------------------//
type AssignmentDetailsForStudentRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AssignmentId  int64                  `protobuf:"varint,1,opt,name=assignment_id,json=assignmentId,proto3" json:"assignment_id,omitempty"`
	StudentId     int64                  `protobuf:"varint,2,opt,name=student_id,json=studentId,proto3" json:"student_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssignmentDetailsForStudentRequest) Reset() {
	*x = AssignmentDetailsForStudentRequest{}
	mi := &file_pb_assignment_assignment_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssignmentDetailsForStudentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignmentDetailsForStudentRequest) ProtoMessage() {}

func (x *AssignmentDetailsForStudentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_assignment_assignment_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignmentDetailsForStudentRequest.ProtoReflect.Descriptor instead.
func (*AssignmentDetailsForStudentRequest) Descriptor() ([]byte, []int) {
	return file_pb_assignment_assignment_proto_rawDescGZIP(), []int{27}
}

func (x *AssignmentDetailsForStudentRequest) GetAssignmentId() int64 {
	if x != nil {
		return x.AssignmentId
	}
	return 0
}

func (x *AssignmentDetailsForStudentRequest) GetStudentId() int64 {
	if x != nil {
		return x.StudentId
	}
	return 0
}

type DeadlineStatus struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IsOverdue     bool                   `protobuf:"varint,1,opt,name=is_overdue,json=isOverdue,proto3" json:"is_overdue,omitempty"`
	DaysRemaining int32                  `protobuf:"varint,2,opt,name=days_remaining,json=daysRemaining,proto3" json:"days_remaining,omitempty"` // Negative if overdue
	StatusText    string                 `protobuf:"bytes,3,opt,name=status_text,json=statusText,proto3" json:"status_text,omitempty"`           // "Overdue", "Due today", "Due in X days", etc.
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeadlineStatus) Reset() {
	*x = DeadlineStatus{}
	mi := &file_pb_assignment_assignment_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeadlineStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeadlineStatus) ProtoMessage() {}

func (x *DeadlineStatus) ProtoReflect() protoreflect.Message {
	mi := &file_pb_assignment_assignment_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeadlineStatus.ProtoReflect.Descriptor instead.
func (*DeadlineStatus) Descriptor() ([]byte, []int) {
	return file_pb_assignment_assignment_proto_rawDescGZIP(), []int{28}
}

func (x *DeadlineStatus) GetIsOverdue() bool {
	if x != nil {
		return x.IsOverdue
	}
	return false
}

func (x *DeadlineStatus) GetDaysRemaining() int32 {
	if x != nil {
		return x.DaysRemaining
	}
	return 0
}

func (x *DeadlineStatus) GetStatusText() string {
	if x != nil {
		return x.StatusText
	}
	return ""
}

type AssignmentDetailsForStudentResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Assignment details
	Assignment *AssignmentResponse `protobuf:"bytes,1,opt,name=assignment,proto3" json:"assignment,omitempty"`
	// Assignment group details
	AssignmentGroup *AssignmentGroupResponse `protobuf:"bytes,2,opt,name=assignment_group,json=assignmentGroup,proto3" json:"assignment_group,omitempty"`
	// Submission details (if exists)
	Submission *AssignmentSubmissionResponse `protobuf:"bytes,3,opt,name=submission,proto3" json:"submission,omitempty"`
	// Deadline information
	DeadlineStatus *DeadlineStatus `protobuf:"bytes,4,opt,name=deadline_status,json=deadlineStatus,proto3" json:"deadline_status,omitempty"`
	// Attachments
	Attachments   []*AssignmentAttachmentResponse `protobuf:"bytes,5,rep,name=attachments,proto3" json:"attachments,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssignmentDetailsForStudentResponse) Reset() {
	*x = AssignmentDetailsForStudentResponse{}
	mi := &file_pb_assignment_assignment_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssignmentDetailsForStudentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignmentDetailsForStudentResponse) ProtoMessage() {}

func (x *AssignmentDetailsForStudentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_assignment_assignment_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignmentDetailsForStudentResponse.ProtoReflect.Descriptor instead.
func (*AssignmentDetailsForStudentResponse) Descriptor() ([]byte, []int) {
	return file_pb_assignment_assignment_proto_rawDescGZIP(), []int{29}
}

func (x *AssignmentDetailsForStudentResponse) GetAssignment() *AssignmentResponse {
	if x != nil {
		return x.Assignment
	}
	return nil
}

func (x *AssignmentDetailsForStudentResponse) GetAssignmentGroup() *AssignmentGroupResponse {
	if x != nil {
		return x.AssignmentGroup
	}
	return nil
}

func (x *AssignmentDetailsForStudentResponse) GetSubmission() *AssignmentSubmissionResponse {
	if x != nil {
		return x.Submission
	}
	return nil
}

func (x *AssignmentDetailsForStudentResponse) GetDeadlineStatus() *DeadlineStatus {
	if x != nil {
		return x.DeadlineStatus
	}
	return nil
}

func (x *AssignmentDetailsForStudentResponse) GetAttachments() []*AssignmentAttachmentResponse {
	if x != nil {
		return x.Attachments
	}
	return nil
}

// -------------------------------//
// Thread Gradebook Messages     //
// -------------------------------//
type ThreadGradebookRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ThreadId      int64                  `protobuf:"varint,1,opt,name=thread_id,json=threadId,proto3" json:"thread_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ThreadGradebookRequest) Reset() {
	*x = ThreadGradebookRequest{}
	mi := &file_pb_assignment_assignment_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ThreadGradebookRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThreadGradebookRequest) ProtoMessage() {}

func (x *ThreadGradebookRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_assignment_assignment_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThreadGradebookRequest.ProtoReflect.Descriptor instead.
func (*ThreadGradebookRequest) Descriptor() ([]byte, []int) {
	return file_pb_assignment_assignment_proto_rawDescGZIP(), []int{30}
}

func (x *ThreadGradebookRequest) GetThreadId() int64 {
	if x != nil {
		return x.ThreadId
	}
	return 0
}

type AssignmentInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Title         string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	MaxPoints     int32                  `protobuf:"varint,3,opt,name=max_points,json=maxPoints,proto3" json:"max_points,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssignmentInfo) Reset() {
	*x = AssignmentInfo{}
	mi := &file_pb_assignment_assignment_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssignmentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignmentInfo) ProtoMessage() {}

func (x *AssignmentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pb_assignment_assignment_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignmentInfo.ProtoReflect.Descriptor instead.
func (*AssignmentInfo) Descriptor() ([]byte, []int) {
	return file_pb_assignment_assignment_proto_rawDescGZIP(), []int{31}
}

func (x *AssignmentInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AssignmentInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *AssignmentInfo) GetMaxPoints() int32 {
	if x != nil {
		return x.MaxPoints
	}
	return 0
}

type Grade struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AssignmentId  int64                  `protobuf:"varint,1,opt,name=assignment_id,json=assignmentId,proto3" json:"assignment_id,omitempty"`
	Score         int32                  `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`                       // 0 if no grade, use has_score to check if grade exists
	HasScore      bool                   `protobuf:"varint,3,opt,name=has_score,json=hasScore,proto3" json:"has_score,omitempty"` // true if score is set, false if no grade
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Grade) Reset() {
	*x = Grade{}
	mi := &file_pb_assignment_assignment_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Grade) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Grade) ProtoMessage() {}

func (x *Grade) ProtoReflect() protoreflect.Message {
	mi := &file_pb_assignment_assignment_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Grade.ProtoReflect.Descriptor instead.
func (*Grade) Descriptor() ([]byte, []int) {
	return file_pb_assignment_assignment_proto_rawDescGZIP(), []int{32}
}

func (x *Grade) GetAssignmentId() int64 {
	if x != nil {
		return x.AssignmentId
	}
	return 0
}

func (x *Grade) GetScore() int32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *Grade) GetHasScore() bool {
	if x != nil {
		return x.HasScore
	}
	return false
}

type StudentGrades struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Surname       string                 `protobuf:"bytes,3,opt,name=surname,proto3" json:"surname,omitempty"`
	Grades        []*Grade               `protobuf:"bytes,4,rep,name=grades,proto3" json:"grades,omitempty"`
	FinalGrade    float64                `protobuf:"fixed64,5,opt,name=final_grade,json=finalGrade,proto3" json:"final_grade,omitempty"`           // 0 if no final grade, use has_final_grade to check
	HasFinalGrade bool                   `protobuf:"varint,6,opt,name=has_final_grade,json=hasFinalGrade,proto3" json:"has_final_grade,omitempty"` // true if final_grade is set, false if no final grade
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StudentGrades) Reset() {
	*x = StudentGrades{}
	mi := &file_pb_assignment_assignment_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StudentGrades) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StudentGrades) ProtoMessage() {}

func (x *StudentGrades) ProtoReflect() protoreflect.Message {
	mi := &file_pb_assignment_assignment_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StudentGrades.ProtoReflect.Descriptor instead.
func (*StudentGrades) Descriptor() ([]byte, []int) {
	return file_pb_assignment_assignment_proto_rawDescGZIP(), []int{33}
}

func (x *StudentGrades) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *StudentGrades) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *StudentGrades) GetSurname() string {
	if x != nil {
		return x.Surname
	}
	return ""
}

func (x *StudentGrades) GetGrades() []*Grade {
	if x != nil {
		return x.Grades
	}
	return nil
}

func (x *StudentGrades) GetFinalGrade() float64 {
	if x != nil {
		return x.FinalGrade
	}
	return 0
}

func (x *StudentGrades) GetHasFinalGrade() bool {
	if x != nil {
		return x.HasFinalGrade
	}
	return false
}

type ThreadGradebookResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Assignments   []*AssignmentInfo      `protobuf:"bytes,1,rep,name=assignments,proto3" json:"assignments,omitempty"`
	Students      []*StudentGrades       `protobuf:"bytes,2,rep,name=students,proto3" json:"students,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ThreadGradebookResponse) Reset() {
	*x = ThreadGradebookResponse{}
	mi := &file_pb_assignment_assignment_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ThreadGradebookResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThreadGradebookResponse) ProtoMessage() {}

func (x *ThreadGradebookResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pb_assignment_assignment_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThreadGradebookResponse.ProtoReflect.Descriptor instead.
func (*ThreadGradebookResponse) Descriptor() ([]byte, []int) {
	return file_pb_assignment_assignment_proto_rawDescGZIP(), []int{34}
}

func (x *ThreadGradebookResponse) GetAssignments() []*AssignmentInfo {
	if x != nil {
		return x.Assignments
	}
	return nil
}

func (x *ThreadGradebookResponse) GetStudents() []*StudentGrades {
	if x != nil {
		return x.Students
	}
	return nil
}

var File_pb_assignment_assignment_proto protoreflect.FileDescriptor

const file_pb_assignment_assignment_proto_rawDesc = "" +
	"\n" +
	"\x1epb/assignment/assignment.proto\x12\fassignmentpb\x1a\x1fgoogle/protobuf/timestamp.proto\"\xe4\x01\n" +
	"\x1cAssignmentAttachmentResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12#\n" +
	"\rassignment_id\x18\x02 \x01(\x03R\fassignmentId\x12\x19\n" +
	"\bfile_url\x18\x03 \x01(\tR\afileUrl\x129\n" +
	"\n" +
	"created_at\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"m\n" +
	"\x1dAssignmentAttachmentsResponse\x12L\n" +
	"\vattachments\x18\x01 \x03(\v2*.assignmentpb.AssignmentAttachmentResponseR\vattachments\"c\n" +
	"!CreateAssignmentAttachmentRequest\x12#\n" +
	"\rassignment_id\x18\x01 \x01(\x03R\fassignmentId\x12\x19\n" +
	"\bfile_url\x18\x02 \x01(\tR\afileUrl\"F\n" +
	"\x1fAssignmentAttachmentByIDRequest\x12#\n" +
	"\rattachment_id\x18\x01 \x01(\x03R\fattachmentId\":\n" +
	"\x13AssignmentIDRequest\x12#\n" +
	"\rassignment_id\x18\x01 \x01(\x03R\fassignmentId\"\x8a\x03\n" +
	"\x1cAssignmentSubmissionResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12#\n" +
	"\rassignment_id\x18\x02 \x01(\x03R\fassignmentId\x12\x17\n" +
	"\auser_id\x18\x03 \x01(\x03R\x06userId\x12=\n" +
	"\fsubmitted_at\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\vsubmittedAt\x12\x1b\n" +
	"\tfile_urls\x18\x05 \x03(\tR\bfileUrls\x12\x18\n" +
	"\acomment\x18\x06 \x01(\tR\acomment\x12\x14\n" +
	"\x05score\x18\a \x01(\x05R\x05score\x12\x1a\n" +
	"\bfeedback\x18\b \x01(\tR\bfeedback\x129\n" +
	"\n" +
	"created_at\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"m\n" +
	"\x1dAssignmentSubmissionsResponse\x12L\n" +
	"\vsubmissions\x18\x01 \x03(\v2*.assignmentpb.AssignmentSubmissionResponseR\vsubmissions\"\x98\x01\n" +
	"!CreateAssignmentSubmissionRequest\x12#\n" +
	"\rassignment_id\x18\x01 \x01(\x03R\fassignmentId\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\x03R\x06userId\x12\x1b\n" +
	"\tfile_urls\x18\x03 \x03(\tR\bfileUrls\x12\x18\n" +
	"\acomment\x18\x04 \x01(\tR\acomment\"F\n" +
	"\x1fAssignmentSubmissionByIDRequest\x12#\n" +
	"\rsubmission_id\x18\x01 \x01(\x03R\fsubmissionId\"\x7f\n" +
	"&UpdateAssignmentSubmissionScoreRequest\x12#\n" +
	"\rsubmission_id\x18\x01 \x01(\x03R\fsubmissionId\x12\x14\n" +
	"\x05score\x18\x02 \x01(\x05R\x05score\x12\x1a\n" +
	"\bfeedback\x18\x03 \x01(\tR\bfeedback\"\x87\x02\n" +
	"\x17AssignmentGroupResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1b\n" +
	"\tthread_id\x18\x02 \x01(\x03R\bthreadId\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x1d\n" +
	"\n" +
	"group_type\x18\x04 \x01(\tR\tgroupType\x12\x16\n" +
	"\x06weight\x18\x05 \x01(\x02R\x06weight\x129\n" +
	"\n" +
	"created_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"n\n" +
	"\x18AssignmentGroupsResponse\x12R\n" +
	"\x11assignment_groups\x18\x01 \x03(\v2%.assignmentpb.AssignmentGroupResponseR\x10assignmentGroups\":\n" +
	"\x13AssignmentGroupByID\x12#\n" +
	"\rassignment_id\x18\x01 \x01(\x03R\fassignmentId\"\x80\x01\n" +
	"\x16AssignmentGroupRequest\x12\x1b\n" +
	"\tthread_id\x18\x01 \x01(\x03R\bthreadId\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1d\n" +
	"\n" +
	"group_type\x18\x03 \x01(\tR\tgroupType\x12\x16\n" +
	"\x06weight\x18\x04 \x01(\x02R\x06weight\"\x96\x01\n" +
	"\x1cAssignmentGroupUpdateRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1b\n" +
	"\tthread_id\x18\x02 \x01(\x03R\bthreadId\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x1d\n" +
	"\n" +
	"group_type\x18\x04 \x01(\tR\tgroupType\x12\x16\n" +
	"\x06weight\x18\x05 \x01(\x02R\x06weight\"8\n" +
	"\x19AssignmentGroupsForThread\x12\x1b\n" +
	"\tthread_id\x18\x01 \x01(\x03R\bthreadId\"\x19\n" +
	"\x17AssignmentEmptyResponse\"\x18\n" +
	"\x16AssignmentEmptyRequest\"\x85\x03\n" +
	"\x12AssignmentResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x17\n" +
	"\aweek_id\x18\x02 \x01(\x03R\x06weekId\x12\x14\n" +
	"\x05title\x18\x03 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\x125\n" +
	"\bdue_date\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\adueDate\x12\x1d\n" +
	"\n" +
	"max_points\x18\x06 \x01(\x05R\tmaxPoints\x12.\n" +
	"\x13assignment_group_id\x18\a \x01(\x03R\x11assignmentGroupId\x12\x12\n" +
	"\x04type\x18\b \x01(\tR\x04type\x129\n" +
	"\n" +
	"created_at\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"Y\n" +
	"\x13AssignmentsResponse\x12B\n" +
	"\vassignments\x18\x01 \x03(\v2 .assignmentpb.AssignmentResponseR\vassignments\"\xfe\x01\n" +
	"\x11AssignmentRequest\x12\x17\n" +
	"\aweek_id\x18\x01 \x01(\x03R\x06weekId\x12\x14\n" +
	"\x05title\x18\x02 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x125\n" +
	"\bdue_date\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\adueDate\x12\x1d\n" +
	"\n" +
	"max_points\x18\x05 \x01(\x05R\tmaxPoints\x12.\n" +
	"\x13assignment_group_id\x18\x06 \x01(\x03R\x11assignmentGroupId\x12\x12\n" +
	"\x04type\x18\a \x01(\tR\x04type\"\x94\x02\n" +
	"\x17AssignmentUpdateRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x17\n" +
	"\aweek_id\x18\x02 \x01(\x03R\x06weekId\x12\x14\n" +
	"\x05title\x18\x03 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\x125\n" +
	"\bdue_date\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\adueDate\x12\x1d\n" +
	"\n" +
	"max_points\x18\x06 \x01(\x05R\tmaxPoints\x12.\n" +
	"\x13assignment_group_id\x18\a \x01(\x03R\x11assignmentGroupId\x12\x12\n" +
	"\x04type\x18\b \x01(\tR\x04type\"4\n" +
	"\x19AssignmentsForWeekRequest\x12\x17\n" +
	"\aweek_id\x18\x01 \x01(\x03R\x06weekId\" \n" +
	"\x0eAssignmentByID\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"X\n" +
	" AssignmentsWithSubmissionRequest\x12\x1b\n" +
	"\tthread_id\x18\x01 \x01(\x03R\bthreadId\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\x03R\x06userId\"\xa8\x01\n" +
	"\x18AssignmentWithSubmission\x12@\n" +
	"\n" +
	"assignment\x18\x01 \x01(\v2 .assignmentpb.AssignmentResponseR\n" +
	"assignment\x12J\n" +
	"\n" +
	"submission\x18\x02 \x01(\v2*.assignmentpb.AssignmentSubmissionResponseR\n" +
	"submission\"a\n" +
	"!AssignmentsWithSubmissionResponse\x12<\n" +
	"\x05items\x18\x01 \x03(\v2&.assignmentpb.AssignmentWithSubmissionR\x05items\"h\n" +
	"\"AssignmentDetailsForStudentRequest\x12#\n" +
	"\rassignment_id\x18\x01 \x01(\x03R\fassignmentId\x12\x1d\n" +
	"\n" +
	"student_id\x18\x02 \x01(\x03R\tstudentId\"w\n" +
	"\x0eDeadlineStatus\x12\x1d\n" +
	"\n" +
	"is_overdue\x18\x01 \x01(\bR\tisOverdue\x12%\n" +
	"\x0edays_remaining\x18\x02 \x01(\x05R\rdaysRemaining\x12\x1f\n" +
	"\vstatus_text\x18\x03 \x01(\tR\n" +
	"statusText\"\x9a\x03\n" +
	"#AssignmentDetailsForStudentResponse\x12@\n" +
	"\n" +
	"assignment\x18\x01 \x01(\v2 .assignmentpb.AssignmentResponseR\n" +
	"assignment\x12P\n" +
	"\x10assignment_group\x18\x02 \x01(\v2%.assignmentpb.AssignmentGroupResponseR\x0fassignmentGroup\x12J\n" +
	"\n" +
	"submission\x18\x03 \x01(\v2*.assignmentpb.AssignmentSubmissionResponseR\n" +
	"submission\x12E\n" +
	"\x0fdeadline_status\x18\x04 \x01(\v2\x1c.assignmentpb.DeadlineStatusR\x0edeadlineStatus\x12L\n" +
	"\vattachments\x18\x05 \x03(\v2*.assignmentpb.AssignmentAttachmentResponseR\vattachments\"5\n" +
	"\x16ThreadGradebookRequest\x12\x1b\n" +
	"\tthread_id\x18\x01 \x01(\x03R\bthreadId\"U\n" +
	"\x0eAssignmentInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x14\n" +
	"\x05title\x18\x02 \x01(\tR\x05title\x12\x1d\n" +
	"\n" +
	"max_points\x18\x03 \x01(\x05R\tmaxPoints\"_\n" +
	"\x05Grade\x12#\n" +
	"\rassignment_id\x18\x01 \x01(\x03R\fassignmentId\x12\x14\n" +
	"\x05score\x18\x02 \x01(\x05R\x05score\x12\x1b\n" +
	"\thas_score\x18\x03 \x01(\bR\bhasScore\"\xc3\x01\n" +
	"\rStudentGrades\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x18\n" +
	"\asurname\x18\x03 \x01(\tR\asurname\x12+\n" +
	"\x06grades\x18\x04 \x03(\v2\x13.assignmentpb.GradeR\x06grades\x12\x1f\n" +
	"\vfinal_grade\x18\x05 \x01(\x01R\n" +
	"finalGrade\x12&\n" +
	"\x0fhas_final_grade\x18\x06 \x01(\bR\rhasFinalGrade\"\x92\x01\n" +
	"\x17ThreadGradebookResponse\x12>\n" +
	"\vassignments\x18\x01 \x03(\v2\x1c.assignmentpb.AssignmentInfoR\vassignments\x127\n" +
	"\bstudents\x18\x02 \x03(\v2\x1b.assignmentpb.StudentGradesR\bstudents2\x83\x14\n" +
	"\x11AssignmentService\x12f\n" +
	"\x15CreateAssignmentGroup\x12$.assignmentpb.AssignmentGroupRequest\x1a%.assignmentpb.AssignmentGroupResponse\"\x00\x12r\n" +
	"\x1dListAssignmentGroupsForThread\x12'.assignmentpb.AssignmentGroupsForThread\x1a&.assignmentpb.AssignmentGroupsResponse\"\x00\x12d\n" +
	"\x16GetAssignmentGroupByID\x12!.assignmentpb.AssignmentGroupByID\x1a%.assignmentpb.AssignmentGroupResponse\"\x00\x12p\n" +
	"\x19UpdateAssignmentGroupByID\x12*.assignmentpb.AssignmentGroupUpdateRequest\x1a%.assignmentpb.AssignmentGroupResponse\"\x00\x12g\n" +
	"\x19DeleteAssignmentGroupByID\x12!.assignmentpb.AssignmentGroupByID\x1a%.assignmentpb.AssignmentEmptyResponse\"\x00\x12W\n" +
	"\x10CreateAssignment\x12\x1f.assignmentpb.AssignmentRequest\x1a .assignmentpb.AssignmentResponse\"\x00\x12f\n" +
	"\x16ListAssignmentsForWeek\x12'.assignmentpb.AssignmentsForWeekRequest\x1a!.assignmentpb.AssignmentsResponse\"\x00\x12U\n" +
	"\x11GetAssignmentByID\x12\x1c.assignmentpb.AssignmentByID\x1a .assignmentpb.AssignmentResponse\"\x00\x12a\n" +
	"\x14UpdateAssignmentByID\x12%.assignmentpb.AssignmentUpdateRequest\x1a .assignmentpb.AssignmentResponse\"\x00\x12]\n" +
	"\x14DeleteAssignmentByID\x12\x1c.assignmentpb.AssignmentByID\x1a%.assignmentpb.AssignmentEmptyResponse\"\x00\x12{\n" +
	"\x1aCreateAssignmentAttachment\x12/.assignmentpb.CreateAssignmentAttachmentRequest\x1a*.assignmentpb.AssignmentAttachmentResponse\"\x00\x12z\n" +
	"&GetAssignmentAttachmentsByAssignmentID\x12!.assignmentpb.AssignmentIDRequest\x1a+.assignmentpb.AssignmentAttachmentsResponse\"\x00\x12x\n" +
	"\x1eDeleteAssignmentAttachmentByID\x12-.assignmentpb.AssignmentAttachmentByIDRequest\x1a%.assignmentpb.AssignmentEmptyResponse\"\x00\x12{\n" +
	"\x1aCreateAssignmentSubmission\x12/.assignmentpb.CreateAssignmentSubmissionRequest\x1a*.assignmentpb.AssignmentSubmissionResponse\"\x00\x12z\n" +
	"\x1bGetAssignmentSubmissionByID\x12-.assignmentpb.AssignmentSubmissionByIDRequest\x1a*.assignmentpb.AssignmentSubmissionResponse\"\x00\x12{\n" +
	"'ListAssignmentSubmissionsByAssignmentID\x12!.assignmentpb.AssignmentIDRequest\x1a+.assignmentpb.AssignmentSubmissionsResponse\"\x00\x12\x85\x01\n" +
	"\x1fUpdateAssignmentSubmissionScore\x124.assignmentpb.UpdateAssignmentSubmissionScoreRequest\x1a*.assignmentpb.AssignmentSubmissionResponse\"\x00\x12x\n" +
	"\x1eDeleteAssignmentSubmissionByID\x12-.assignmentpb.AssignmentSubmissionByIDRequest\x1a%.assignmentpb.AssignmentEmptyResponse\"\x00\x12\x8b\x01\n" +
	"&ListAssignmentsWithSubmissionForThread\x12..assignmentpb.AssignmentsWithSubmissionRequest\x1a/.assignmentpb.AssignmentsWithSubmissionResponse\"\x00\x12\x8e\x01\n" +
	")ListAssignmentsWithoutSubmissionForThread\x12..assignmentpb.AssignmentsWithSubmissionRequest\x1a/.assignmentpb.AssignmentsWithSubmissionResponse\"\x00\x12\x87\x01\n" +
	"\x1eGetAssignmentDetailsForStudent\x120.assignmentpb.AssignmentDetailsForStudentRequest\x1a1.assignmentpb.AssignmentDetailsForStudentResponse\"\x00\x12c\n" +
	"\x12GetThreadGradebook\x12$.assignmentpb.ThreadGradebookRequest\x1a%.assignmentpb.ThreadGradebookResponse\"\x00BMZKgithub.com/olzzhas/edunite-server/course_service/pb/assignment;assignmentpbb\x06proto3"

var (
	file_pb_assignment_assignment_proto_rawDescOnce sync.Once
	file_pb_assignment_assignment_proto_rawDescData []byte
)

func file_pb_assignment_assignment_proto_rawDescGZIP() []byte {
	file_pb_assignment_assignment_proto_rawDescOnce.Do(func() {
		file_pb_assignment_assignment_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pb_assignment_assignment_proto_rawDesc), len(file_pb_assignment_assignment_proto_rawDesc)))
	})
	return file_pb_assignment_assignment_proto_rawDescData
}

var file_pb_assignment_assignment_proto_msgTypes = make([]protoimpl.MessageInfo, 35)
var file_pb_assignment_assignment_proto_goTypes = []any{
	(*AssignmentAttachmentResponse)(nil),           // 0: assignmentpb.AssignmentAttachmentResponse
	(*AssignmentAttachmentsResponse)(nil),          // 1: assignmentpb.AssignmentAttachmentsResponse
	(*CreateAssignmentAttachmentRequest)(nil),      // 2: assignmentpb.CreateAssignmentAttachmentRequest
	(*AssignmentAttachmentByIDRequest)(nil),        // 3: assignmentpb.AssignmentAttachmentByIDRequest
	(*AssignmentIDRequest)(nil),                    // 4: assignmentpb.AssignmentIDRequest
	(*AssignmentSubmissionResponse)(nil),           // 5: assignmentpb.AssignmentSubmissionResponse
	(*AssignmentSubmissionsResponse)(nil),          // 6: assignmentpb.AssignmentSubmissionsResponse
	(*CreateAssignmentSubmissionRequest)(nil),      // 7: assignmentpb.CreateAssignmentSubmissionRequest
	(*AssignmentSubmissionByIDRequest)(nil),        // 8: assignmentpb.AssignmentSubmissionByIDRequest
	(*UpdateAssignmentSubmissionScoreRequest)(nil), // 9: assignmentpb.UpdateAssignmentSubmissionScoreRequest
	(*AssignmentGroupResponse)(nil),                // 10: assignmentpb.AssignmentGroupResponse
	(*AssignmentGroupsResponse)(nil),               // 11: assignmentpb.AssignmentGroupsResponse
	(*AssignmentGroupByID)(nil),                    // 12: assignmentpb.AssignmentGroupByID
	(*AssignmentGroupRequest)(nil),                 // 13: assignmentpb.AssignmentGroupRequest
	(*AssignmentGroupUpdateRequest)(nil),           // 14: assignmentpb.AssignmentGroupUpdateRequest
	(*AssignmentGroupsForThread)(nil),              // 15: assignmentpb.AssignmentGroupsForThread
	(*AssignmentEmptyResponse)(nil),                // 16: assignmentpb.AssignmentEmptyResponse
	(*AssignmentEmptyRequest)(nil),                 // 17: assignmentpb.AssignmentEmptyRequest
	(*AssignmentResponse)(nil),                     // 18: assignmentpb.AssignmentResponse
	(*AssignmentsResponse)(nil),                    // 19: assignmentpb.AssignmentsResponse
	(*AssignmentRequest)(nil),                      // 20: assignmentpb.AssignmentRequest
	(*AssignmentUpdateRequest)(nil),                // 21: assignmentpb.AssignmentUpdateRequest
	(*AssignmentsForWeekRequest)(nil),              // 22: assignmentpb.AssignmentsForWeekRequest
	(*AssignmentByID)(nil),                         // 23: assignmentpb.AssignmentByID
	(*AssignmentsWithSubmissionRequest)(nil),       // 24: assignmentpb.AssignmentsWithSubmissionRequest
	(*AssignmentWithSubmission)(nil),               // 25: assignmentpb.AssignmentWithSubmission
	(*AssignmentsWithSubmissionResponse)(nil),      // 26: assignmentpb.AssignmentsWithSubmissionResponse
	(*AssignmentDetailsForStudentRequest)(nil),     // 27: assignmentpb.AssignmentDetailsForStudentRequest
	(*DeadlineStatus)(nil),                         // 28: assignmentpb.DeadlineStatus
	(*AssignmentDetailsForStudentResponse)(nil),    // 29: assignmentpb.AssignmentDetailsForStudentResponse
	(*ThreadGradebookRequest)(nil),                 // 30: assignmentpb.ThreadGradebookRequest
	(*AssignmentInfo)(nil),                         // 31: assignmentpb.AssignmentInfo
	(*Grade)(nil),                                  // 32: assignmentpb.Grade
	(*StudentGrades)(nil),                          // 33: assignmentpb.StudentGrades
	(*ThreadGradebookResponse)(nil),                // 34: assignmentpb.ThreadGradebookResponse
	(*timestamppb.Timestamp)(nil),                  // 35: google.protobuf.Timestamp
}
var file_pb_assignment_assignment_proto_depIdxs = []int32{
	35, // 0: assignmentpb.AssignmentAttachmentResponse.created_at:type_name -> google.protobuf.Timestamp
	35, // 1: assignmentpb.AssignmentAttachmentResponse.updated_at:type_name -> google.protobuf.Timestamp
	0,  // 2: assignmentpb.AssignmentAttachmentsResponse.attachments:type_name -> assignmentpb.AssignmentAttachmentResponse
	35, // 3: assignmentpb.AssignmentSubmissionResponse.submitted_at:type_name -> google.protobuf.Timestamp
	35, // 4: assignmentpb.AssignmentSubmissionResponse.created_at:type_name -> google.protobuf.Timestamp
	35, // 5: assignmentpb.AssignmentSubmissionResponse.updated_at:type_name -> google.protobuf.Timestamp
	5,  // 6: assignmentpb.AssignmentSubmissionsResponse.submissions:type_name -> assignmentpb.AssignmentSubmissionResponse
	35, // 7: assignmentpb.AssignmentGroupResponse.created_at:type_name -> google.protobuf.Timestamp
	35, // 8: assignmentpb.AssignmentGroupResponse.updated_at:type_name -> google.protobuf.Timestamp
	10, // 9: assignmentpb.AssignmentGroupsResponse.assignment_groups:type_name -> assignmentpb.AssignmentGroupResponse
	35, // 10: assignmentpb.AssignmentResponse.due_date:type_name -> google.protobuf.Timestamp
	35, // 11: assignmentpb.AssignmentResponse.created_at:type_name -> google.protobuf.Timestamp
	35, // 12: assignmentpb.AssignmentResponse.updated_at:type_name -> google.protobuf.Timestamp
	18, // 13: assignmentpb.AssignmentsResponse.assignments:type_name -> assignmentpb.AssignmentResponse
	35, // 14: assignmentpb.AssignmentRequest.due_date:type_name -> google.protobuf.Timestamp
	35, // 15: assignmentpb.AssignmentUpdateRequest.due_date:type_name -> google.protobuf.Timestamp
	18, // 16: assignmentpb.AssignmentWithSubmission.assignment:type_name -> assignmentpb.AssignmentResponse
	5,  // 17: assignmentpb.AssignmentWithSubmission.submission:type_name -> assignmentpb.AssignmentSubmissionResponse
	25, // 18: assignmentpb.AssignmentsWithSubmissionResponse.items:type_name -> assignmentpb.AssignmentWithSubmission
	18, // 19: assignmentpb.AssignmentDetailsForStudentResponse.assignment:type_name -> assignmentpb.AssignmentResponse
	10, // 20: assignmentpb.AssignmentDetailsForStudentResponse.assignment_group:type_name -> assignmentpb.AssignmentGroupResponse
	5,  // 21: assignmentpb.AssignmentDetailsForStudentResponse.submission:type_name -> assignmentpb.AssignmentSubmissionResponse
	28, // 22: assignmentpb.AssignmentDetailsForStudentResponse.deadline_status:type_name -> assignmentpb.DeadlineStatus
	0,  // 23: assignmentpb.AssignmentDetailsForStudentResponse.attachments:type_name -> assignmentpb.AssignmentAttachmentResponse
	32, // 24: assignmentpb.StudentGrades.grades:type_name -> assignmentpb.Grade
	31, // 25: assignmentpb.ThreadGradebookResponse.assignments:type_name -> assignmentpb.AssignmentInfo
	33, // 26: assignmentpb.ThreadGradebookResponse.students:type_name -> assignmentpb.StudentGrades
	13, // 27: assignmentpb.AssignmentService.CreateAssignmentGroup:input_type -> assignmentpb.AssignmentGroupRequest
	15, // 28: assignmentpb.AssignmentService.ListAssignmentGroupsForThread:input_type -> assignmentpb.AssignmentGroupsForThread
	12, // 29: assignmentpb.AssignmentService.GetAssignmentGroupByID:input_type -> assignmentpb.AssignmentGroupByID
	14, // 30: assignmentpb.AssignmentService.UpdateAssignmentGroupByID:input_type -> assignmentpb.AssignmentGroupUpdateRequest
	12, // 31: assignmentpb.AssignmentService.DeleteAssignmentGroupByID:input_type -> assignmentpb.AssignmentGroupByID
	20, // 32: assignmentpb.AssignmentService.CreateAssignment:input_type -> assignmentpb.AssignmentRequest
	22, // 33: assignmentpb.AssignmentService.ListAssignmentsForWeek:input_type -> assignmentpb.AssignmentsForWeekRequest
	23, // 34: assignmentpb.AssignmentService.GetAssignmentByID:input_type -> assignmentpb.AssignmentByID
	21, // 35: assignmentpb.AssignmentService.UpdateAssignmentByID:input_type -> assignmentpb.AssignmentUpdateRequest
	23, // 36: assignmentpb.AssignmentService.DeleteAssignmentByID:input_type -> assignmentpb.AssignmentByID
	2,  // 37: assignmentpb.AssignmentService.CreateAssignmentAttachment:input_type -> assignmentpb.CreateAssignmentAttachmentRequest
	4,  // 38: assignmentpb.AssignmentService.GetAssignmentAttachmentsByAssignmentID:input_type -> assignmentpb.AssignmentIDRequest
	3,  // 39: assignmentpb.AssignmentService.DeleteAssignmentAttachmentByID:input_type -> assignmentpb.AssignmentAttachmentByIDRequest
	7,  // 40: assignmentpb.AssignmentService.CreateAssignmentSubmission:input_type -> assignmentpb.CreateAssignmentSubmissionRequest
	8,  // 41: assignmentpb.AssignmentService.GetAssignmentSubmissionByID:input_type -> assignmentpb.AssignmentSubmissionByIDRequest
	4,  // 42: assignmentpb.AssignmentService.ListAssignmentSubmissionsByAssignmentID:input_type -> assignmentpb.AssignmentIDRequest
	9,  // 43: assignmentpb.AssignmentService.UpdateAssignmentSubmissionScore:input_type -> assignmentpb.UpdateAssignmentSubmissionScoreRequest
	8,  // 44: assignmentpb.AssignmentService.DeleteAssignmentSubmissionByID:input_type -> assignmentpb.AssignmentSubmissionByIDRequest
	24, // 45: assignmentpb.AssignmentService.ListAssignmentsWithSubmissionForThread:input_type -> assignmentpb.AssignmentsWithSubmissionRequest
	24, // 46: assignmentpb.AssignmentService.ListAssignmentsWithoutSubmissionForThread:input_type -> assignmentpb.AssignmentsWithSubmissionRequest
	27, // 47: assignmentpb.AssignmentService.GetAssignmentDetailsForStudent:input_type -> assignmentpb.AssignmentDetailsForStudentRequest
	30, // 48: assignmentpb.AssignmentService.GetThreadGradebook:input_type -> assignmentpb.ThreadGradebookRequest
	10, // 49: assignmentpb.AssignmentService.CreateAssignmentGroup:output_type -> assignmentpb.AssignmentGroupResponse
	11, // 50: assignmentpb.AssignmentService.ListAssignmentGroupsForThread:output_type -> assignmentpb.AssignmentGroupsResponse
	10, // 51: assignmentpb.AssignmentService.GetAssignmentGroupByID:output_type -> assignmentpb.AssignmentGroupResponse
	10, // 52: assignmentpb.AssignmentService.UpdateAssignmentGroupByID:output_type -> assignmentpb.AssignmentGroupResponse
	16, // 53: assignmentpb.AssignmentService.DeleteAssignmentGroupByID:output_type -> assignmentpb.AssignmentEmptyResponse
	18, // 54: assignmentpb.AssignmentService.CreateAssignment:output_type -> assignmentpb.AssignmentResponse
	19, // 55: assignmentpb.AssignmentService.ListAssignmentsForWeek:output_type -> assignmentpb.AssignmentsResponse
	18, // 56: assignmentpb.AssignmentService.GetAssignmentByID:output_type -> assignmentpb.AssignmentResponse
	18, // 57: assignmentpb.AssignmentService.UpdateAssignmentByID:output_type -> assignmentpb.AssignmentResponse
	16, // 58: assignmentpb.AssignmentService.DeleteAssignmentByID:output_type -> assignmentpb.AssignmentEmptyResponse
	0,  // 59: assignmentpb.AssignmentService.CreateAssignmentAttachment:output_type -> assignmentpb.AssignmentAttachmentResponse
	1,  // 60: assignmentpb.AssignmentService.GetAssignmentAttachmentsByAssignmentID:output_type -> assignmentpb.AssignmentAttachmentsResponse
	16, // 61: assignmentpb.AssignmentService.DeleteAssignmentAttachmentByID:output_type -> assignmentpb.AssignmentEmptyResponse
	5,  // 62: assignmentpb.AssignmentService.CreateAssignmentSubmission:output_type -> assignmentpb.AssignmentSubmissionResponse
	5,  // 63: assignmentpb.AssignmentService.GetAssignmentSubmissionByID:output_type -> assignmentpb.AssignmentSubmissionResponse
	6,  // 64: assignmentpb.AssignmentService.ListAssignmentSubmissionsByAssignmentID:output_type -> assignmentpb.AssignmentSubmissionsResponse
	5,  // 65: assignmentpb.AssignmentService.UpdateAssignmentSubmissionScore:output_type -> assignmentpb.AssignmentSubmissionResponse
	16, // 66: assignmentpb.AssignmentService.DeleteAssignmentSubmissionByID:output_type -> assignmentpb.AssignmentEmptyResponse
	26, // 67: assignmentpb.AssignmentService.ListAssignmentsWithSubmissionForThread:output_type -> assignmentpb.AssignmentsWithSubmissionResponse
	26, // 68: assignmentpb.AssignmentService.ListAssignmentsWithoutSubmissionForThread:output_type -> assignmentpb.AssignmentsWithSubmissionResponse
	29, // 69: assignmentpb.AssignmentService.GetAssignmentDetailsForStudent:output_type -> assignmentpb.AssignmentDetailsForStudentResponse
	34, // 70: assignmentpb.AssignmentService.GetThreadGradebook:output_type -> assignmentpb.ThreadGradebookResponse
	49, // [49:71] is the sub-list for method output_type
	27, // [27:49] is the sub-list for method input_type
	27, // [27:27] is the sub-list for extension type_name
	27, // [27:27] is the sub-list for extension extendee
	0,  // [0:27] is the sub-list for field type_name
}

func init() { file_pb_assignment_assignment_proto_init() }
func file_pb_assignment_assignment_proto_init() {
	if File_pb_assignment_assignment_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pb_assignment_assignment_proto_rawDesc), len(file_pb_assignment_assignment_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   35,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_assignment_assignment_proto_goTypes,
		DependencyIndexes: file_pb_assignment_assignment_proto_depIdxs,
		MessageInfos:      file_pb_assignment_assignment_proto_msgTypes,
	}.Build()
	File_pb_assignment_assignment_proto = out.File
	file_pb_assignment_assignment_proto_goTypes = nil
	file_pb_assignment_assignment_proto_depIdxs = nil
}
