syntax = "proto3";

package analyticspb;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/olzzhas/edunite-server/analytics_service/pb/analytics;analyticspb";

service AnalyticsService {
  // Dashboard Overview
  rpc GetOverview(OverviewRequest) returns (OverviewResponse) {}
  
  // Users Timeline
  rpc GetUsersTimeline(UsersTimelineRequest) returns (UsersTimelineResponse) {}
  
  // User Demographics
  rpc GetUserDemographics(UserDemographicsRequest) returns (UserDemographicsResponse) {}
  
  // Recent Activities
  rpc GetRecentActivities(RecentActivitiesRequest) returns (RecentActivitiesResponse) {}
  
  // Detailed Stats
  rpc GetDetailedStats(DetailedStatsRequest) returns (DetailedStatsResponse) {}
  
  // Performance Metrics
  rpc GetPerformanceMetrics(PerformanceMetricsRequest) returns (PerformanceMetricsResponse) {}
}

// Overview Request/Response
message OverviewRequest {
  string period = 1; // "last_30_days", "last_7_days", etc.
}

message StatValue {
  int64 value = 1;
  string trend = 2; // "up", "down", "neutral"
  string trend_value = 3; // "+6.08%"
  int64 previous_period_value = 4;
}

message OverviewResponse {
  StatValue total_users = 1;
  StatValue total_courses = 2;
  StatValue total_threads = 3;
  StatValue active_students = 4;
  string period = 5;
}

// Users Timeline Request/Response
message UsersTimelineRequest {
  string period = 1; // "7d", "30d", "90d", "1y"
  string granularity = 2; // "day", "week", "month"
}

message TimelinePoint {
  string date = 1;
  int64 total_users = 2;
  int64 new_users = 3;
  int64 active_users = 4;
}

message UsersTimelineResponse {
  repeated TimelinePoint timeline = 1;
  repeated TimelinePoint comparison = 2; // previous period
}

// User Demographics Request/Response
message UserDemographicsRequest {}

message DemographicItem {
  string name = 1;
  double value = 2;
  double percentage = 3;
}

message UserDemographicsResponse {
  repeated DemographicItem devices = 1;
  repeated DemographicItem locations = 2;
}

// Recent Activities Request/Response
message RecentActivitiesRequest {
  int32 limit = 1;
  string type = 2; // "enrollment", "assignment", "login", "all"
}

message ActivityUser {
  int64 id = 1;
  string name = 2;
  string email = 3;
}

message ActivityCourse {
  int64 id = 1;
  string title = 2;
}

message ActivityAssignment {
  int64 id = 1;
  string title = 2;
}

message Activity {
  string id = 1;
  string type = 2;
  ActivityUser user = 3;
  ActivityCourse course = 4;
  ActivityAssignment assignment = 5;
  google.protobuf.Timestamp date = 6;
  string status = 7;
}

message RecentActivitiesResponse {
  repeated Activity activities = 1;
}

// Detailed Stats Request/Response
message DetailedStatsRequest {
  string period = 1;
  repeated string metrics = 2; // "revenue", "activity", "performance"
}

message CourseEnrollmentPoint {
  string date = 1;
  int64 enrollments = 2;
  int64 completions = 3;
}

message CourseEnrollments {
  repeated CourseEnrollmentPoint timeline = 1;
  repeated CourseEnrollmentPoint comparison = 2;
}

message UserActivityDay {
  string day = 1;
  int64 active_users = 2;
}

message UserActivity {
  repeated UserActivityDay daily = 1;
}

message TrafficSource {
  string source = 1;
  double value = 2;
  double percentage = 3;
}

message DetailedStatsResponse {
  CourseEnrollments course_enrollments = 1;
  UserActivity user_activity = 2;
  repeated TrafficSource traffic_sources = 3;
}

// Performance Metrics Request/Response
message PerformanceMetricsRequest {}

message PerformanceMetric {
  double value = 1;
  string unit = 2;
  string trend = 3;
  string change = 4;
  string description = 5;
}

message PerformanceMetricsResponse {
  PerformanceMetric course_completion_rate = 1;
  PerformanceMetric average_grade = 2;
  PerformanceMetric attendance_rate = 3;
  PerformanceMetric average_session_duration = 4;
}
