package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/olzzhas/edunite-server/gateway/clients"
	"github.com/olzzhas/edunite-server/gateway/handlers"
)

func SetupNotificationRoutes(r *gin.Engine, authClient *clients.AuthClient, notificationHandler *handlers.NotificationHandler) {
	// Public routes (no authentication required)
	// None for notifications - all require authentication

	// Protected routes (authentication required)
	protected := r.Group("/")
	protected.Use(AuthMiddleware(authClient))

	// User notification routes (users can access their own notifications)
	protected.GET("/users/:user_id/notifications", notificationHandler.GetUserNotificationsHandler)
	protected.GET("/users/:user_id/notifications/stats", notificationHandler.GetNotificationStatsHandler)
	protected.PUT("/notifications/:notification_id/read", notificationHandler.MarkAsReadHandler)

	// Admin-only routes
	adminOnly := r.Group("/")
	adminOnly.Use(AuthMiddleware(authClient))
	adminOnly.Use(RoleMiddleware(authClient, "admin"))

	// Admin notification management
	adminOnly.POST("/notifications", notificationHandler.CreateNotificationHandler)
	adminOnly.GET("/notifications", notificationHandler.GetAllNotificationsHandler)
	adminOnly.DELETE("/notifications/:notification_id", notificationHandler.DeleteNotificationHandler)
	adminOnly.GET("/email-templates", notificationHandler.GetEmailTemplatesHandler)

	// Teacher routes (teachers can create notifications for their courses/threads)
	teacherRoutes := r.Group("/")
	teacherRoutes.Use(AuthMiddleware(authClient))
	teacherRoutes.Use(RoleMiddleware(authClient, "teacher", "admin"))

	// Teachers can create notifications for their courses/threads
	teacherRoutes.POST("/teacher/notifications", notificationHandler.CreateNotificationHandler)
}
