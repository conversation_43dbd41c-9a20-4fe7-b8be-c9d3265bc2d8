package config

import (
	"log"
	"os"
	"strconv"

	"github.com/joho/godotenv"
)

type DatabaseConfig struct {
	URL string
}

type EmailConfig struct {
	SMTPHost     string
	SMTPPort     int
	SMTPUsername string
	SMTPPassword string
	FromEmail    string
	FromName     string
}

type ServerConfig struct {
	Port string
}

type Config struct {
	Database DatabaseConfig
	Email    EmailConfig
	Server   ServerConfig
}

func LoadConfig() *Config {
	// Load .env file if it exists
	if err := godotenv.Load(); err != nil {
		log.Printf("Warning: .env file not found: %v", err)
	}

	// Parse SMTP port
	smtpPort, err := strconv.Atoi(getEnvOrDefault("SMTP_PORT", "587"))
	if err != nil {
		log.Printf("Warning: Invalid SMTP_PORT, using default 587: %v", err)
		smtpPort = 587
	}

	return &Config{
		Database: DatabaseConfig{
			URL: getEnvOrDefault("DATABASE_URL", "******************************************************/edunite"),
		},
		Email: EmailConfig{
			SMTPHost:     getEnvOrDefault("SMTP_HOST", "smtp.gmail.com"),
			SMTPPort:     smtpPort,
			SMTPUsername: getEnvOrDefault("SMTP_USERNAME", ""),
			SMTPPassword: getEnvOrDefault("SMTP_PASSWORD", ""),
			FromEmail:    getEnvOrDefault("FROM_EMAIL", "<EMAIL>"),
			FromName:     getEnvOrDefault("FROM_NAME", "EduNite"),
		},
		Server: ServerConfig{
			Port: getEnvOrDefault("SERVER_PORT", "50056"),
		},
	}
}

func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
