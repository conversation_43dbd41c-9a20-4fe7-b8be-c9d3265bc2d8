syntax = "proto3";

package assignmentpb;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/olzzhas/edunite-server/course_service/pb/assignment;assignmentpb";


service AssignmentService {
  // assignment groups entity
  rpc CreateAssignmentGroup(AssignmentGroupRequest) returns(AssignmentGroupResponse){}
  rpc ListAssignmentGroupsForThread(AssignmentGroupsForThread) returns(AssignmentGroupsResponse){}
  rpc GetAssignmentGroupByID(AssignmentGroupByID) returns(AssignmentGroupResponse){}
  rpc UpdateAssignmentGroupByID(AssignmentGroupUpdateRequest) returns(AssignmentGroupResponse){}
  rpc DeleteAssignmentGroupByID(AssignmentGroupByID) returns(AssignmentEmptyResponse){}

  // Assignment
  rpc CreateAssignment(AssignmentRequest) returns (AssignmentResponse) {}
  rpc ListAssignmentsForWeek(AssignmentsForWeekRequest) returns (AssignmentsResponse) {}
  rpc GetAssignmentByID(AssignmentByID) returns (AssignmentResponse) {}
  rpc UpdateAssignmentByID(AssignmentUpdateRequest) returns (AssignmentResponse) {}
  rpc DeleteAssignmentByID(AssignmentByID) returns (AssignmentEmptyResponse) {}

  // Attachment RPC
  rpc CreateAssignmentAttachment(CreateAssignmentAttachmentRequest) returns (AssignmentAttachmentResponse) {}
  rpc GetAssignmentAttachmentsByAssignmentID(AssignmentIDRequest) returns (AssignmentAttachmentsResponse) {}
  rpc DeleteAssignmentAttachmentByID(AssignmentAttachmentByIDRequest) returns (AssignmentEmptyResponse) {}

  // Submission RPC
  rpc CreateAssignmentSubmission(CreateAssignmentSubmissionRequest) returns (AssignmentSubmissionResponse) {}
  rpc GetAssignmentSubmissionByID(AssignmentSubmissionByIDRequest) returns (AssignmentSubmissionResponse) {}
  rpc ListAssignmentSubmissionsByAssignmentID(AssignmentIDRequest) returns (AssignmentSubmissionsResponse) {}
  rpc UpdateAssignmentSubmissionScore(UpdateAssignmentSubmissionScoreRequest) returns (AssignmentSubmissionResponse) {}
  rpc DeleteAssignmentSubmissionByID(AssignmentSubmissionByIDRequest) returns (AssignmentEmptyResponse) {}

  rpc ListAssignmentsWithSubmissionForThread(AssignmentsWithSubmissionRequest) returns (AssignmentsWithSubmissionResponse) {}

  // List assignments without submissions for a student
  rpc ListAssignmentsWithoutSubmissionForThread(AssignmentsWithSubmissionRequest) returns (AssignmentsWithSubmissionResponse) {}

  // Comprehensive assignment details for a student
  rpc GetAssignmentDetailsForStudent(AssignmentDetailsForStudentRequest) returns (AssignmentDetailsForStudentResponse) {}

  // Get thread gradebook with all assignments and student grades
  rpc GetThreadGradebook(ThreadGradebookRequest) returns (ThreadGradebookResponse) {}
}


//-------------------------------//
//  Assignment Attachment        //
//-------------------------------//
message AssignmentAttachmentResponse {
  int64 id = 1;
  int64 assignment_id = 2;
  string file_url = 3;
  google.protobuf.Timestamp created_at = 4;
  google.protobuf.Timestamp updated_at = 5;
}

message AssignmentAttachmentsResponse {
  repeated AssignmentAttachmentResponse attachments = 1;
}

// CreateAttachment
message CreateAssignmentAttachmentRequest {
  int64 assignment_id = 1;
  string file_url = 2;
}

// For deletion
message AssignmentAttachmentByIDRequest {
  int64 attachment_id = 1;
}

// For listing
message AssignmentIDRequest {
  int64 assignment_id = 1;
}

//-------------------------------//
//  Assignment Submission        //
//-------------------------------//
message AssignmentSubmissionResponse {
  int64 id = 1;
  int64 assignment_id = 2;
  int64 user_id = 3;
  google.protobuf.Timestamp submitted_at = 4;
  repeated string file_urls = 5;
  string comment = 6;
  int32 score = 7;
  string feedback = 8;
  google.protobuf.Timestamp created_at = 9;
  google.protobuf.Timestamp updated_at = 10;
}

message AssignmentSubmissionsResponse {
  repeated AssignmentSubmissionResponse submissions = 1;
}

// Создать новую сдачу
message CreateAssignmentSubmissionRequest {
  int64 assignment_id = 1;
  int64 user_id = 2;
  repeated string file_urls = 3;
  string comment = 4;
}

// Получить сабмит
message AssignmentSubmissionByIDRequest {
  int64 submission_id = 1;
}

// Обновить оценку
message UpdateAssignmentSubmissionScoreRequest {
  int64 submission_id = 1;
  int32 score = 2;
  string feedback = 3;
}

//-------------------------------//
//  Assignment Group Messages    //
//-------------------------------//
message AssignmentGroupResponse {
  int64 id = 1;
  int64 thread_id = 2;
  string name = 3;
  string group_type = 4;
  float weight = 5;
  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp updated_at = 7;
}

message AssignmentGroupsResponse {
  repeated AssignmentGroupResponse assignment_groups = 1;
}

message AssignmentGroupByID {
  int64 assignment_id = 1;
}

message AssignmentGroupRequest {
  int64 thread_id = 1;
  string name = 2;
  string group_type = 3;
  float weight = 4;
}

message AssignmentGroupUpdateRequest {
  int64 id = 1;
  int64 thread_id = 2;
  string name = 3;
  string group_type = 4;
  float weight = 5;
}

message AssignmentGroupsForThread {
  int64 thread_id = 1;
}

message AssignmentEmptyResponse {

}

message AssignmentEmptyRequest {

}


message AssignmentResponse {
  int64 id = 1;
  int64 week_id = 2;
  string title = 3;
  string description = 4;
  google.protobuf.Timestamp due_date = 5;
  int32 max_points = 6;
  int64 assignment_group_id = 7;
  string type = 8;
  google.protobuf.Timestamp created_at = 9;
  google.protobuf.Timestamp updated_at = 10;
}

message AssignmentsResponse {
  repeated AssignmentResponse assignments = 1;
}

message AssignmentRequest {
  int64 week_id = 1;
  string title = 2;
  string description = 3;
  google.protobuf.Timestamp due_date = 4;
  int32 max_points = 5;
  int64 assignment_group_id = 6; // optional
  string type = 7; // 'task' or 'info'
}

message AssignmentUpdateRequest {
  int64 id = 1;
  int64 week_id = 2;
  string title = 3;
  string description = 4;
  google.protobuf.Timestamp due_date = 5;
  int32 max_points = 6;
  int64 assignment_group_id = 7;
  string type = 8; // 'task' or 'info'
}

message AssignmentsForWeekRequest {
  int64 week_id = 1;
}

message AssignmentByID {
  int64 id = 1;
}

message AssignmentsWithSubmissionRequest {
  int64 thread_id = 1;
  int64 user_id   = 2;
}

message AssignmentWithSubmission {
  AssignmentResponse           assignment = 1;
  AssignmentSubmissionResponse submission = 2;
}

message AssignmentsWithSubmissionResponse {
  repeated AssignmentWithSubmission items = 1;
}

//-------------------------------//
// Comprehensive Assignment Details //
//-------------------------------//
message AssignmentDetailsForStudentRequest {
  int64 assignment_id = 1;
  int64 student_id = 2;
}

message DeadlineStatus {
  bool is_overdue = 1;
  int32 days_remaining = 2;  // Negative if overdue
  string status_text = 3;    // "Overdue", "Due today", "Due in X days", etc.
}

message AssignmentDetailsForStudentResponse {
  // Assignment details
  AssignmentResponse assignment = 1;

  // Assignment group details
  AssignmentGroupResponse assignment_group = 2;

  // Submission details (if exists)
  AssignmentSubmissionResponse submission = 3;

  // Deadline information
  DeadlineStatus deadline_status = 4;

  // Attachments
  repeated AssignmentAttachmentResponse attachments = 5;
}

//-------------------------------//
// Thread Gradebook Messages     //
//-------------------------------//
message ThreadGradebookRequest {
  int64 thread_id = 1;
}

message AssignmentInfo {
  int64 id = 1;
  string title = 2;
  int32 max_points = 3;
}

message Grade {
  int64 assignment_id = 1;
  int32 score = 2;  // 0 if no grade, use has_score to check if grade exists
  bool has_score = 3;  // true if score is set, false if no grade
}

message StudentGrades {
  int64 id = 1;
  string name = 2;
  string surname = 3;
  repeated Grade grades = 4;
  double final_grade = 5;  // 0 if no final grade, use has_final_grade to check
  bool has_final_grade = 6;  // true if final_grade is set, false if no final grade
}

message ThreadGradebookResponse {
  repeated AssignmentInfo assignments = 1;
  repeated StudentGrades students = 2;
}