.PHONY: proto build run clean

# Generate protobuf files
proto:
	protoc --go_out=. --go_opt=paths=source_relative \
		--go-grpc_out=. --go-grpc_opt=paths=source_relative \
		proto/notification.proto

# Build the service
build:
	go build -o bin/notification_service cmd/main.go

# Run the service
run:
	go run cmd/main.go

# Clean generated files
clean:
	rm -rf bin/
	rm -f proto/*.pb.go

# Install dependencies
deps:
	go mod tidy
	go mod download

# Run tests
test:
	go test ./...

# Docker build
docker-build:
	docker build -t notification_service .

# Docker run
docker-run:
	docker run -p 50056:50056 notification_service
