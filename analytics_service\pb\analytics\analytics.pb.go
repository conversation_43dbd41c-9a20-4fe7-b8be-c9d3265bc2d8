// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.28.2
// source: proto/analytics.proto

package analyticspb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Overview Request/Response
type OverviewRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Period        string                 `protobuf:"bytes,1,opt,name=period,proto3" json:"period,omitempty"` // "last_30_days", "last_7_days", etc.
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OverviewRequest) Reset() {
	*x = OverviewRequest{}
	mi := &file_proto_analytics_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OverviewRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OverviewRequest) ProtoMessage() {}

func (x *OverviewRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_analytics_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OverviewRequest.ProtoReflect.Descriptor instead.
func (*OverviewRequest) Descriptor() ([]byte, []int) {
	return file_proto_analytics_proto_rawDescGZIP(), []int{0}
}

func (x *OverviewRequest) GetPeriod() string {
	if x != nil {
		return x.Period
	}
	return ""
}

type StatValue struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	Value               int64                  `protobuf:"varint,1,opt,name=value,proto3" json:"value,omitempty"`
	Trend               string                 `protobuf:"bytes,2,opt,name=trend,proto3" json:"trend,omitempty"`                             // "up", "down", "neutral"
	TrendValue          string                 `protobuf:"bytes,3,opt,name=trend_value,json=trendValue,proto3" json:"trend_value,omitempty"` // "+6.08%"
	PreviousPeriodValue int64                  `protobuf:"varint,4,opt,name=previous_period_value,json=previousPeriodValue,proto3" json:"previous_period_value,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *StatValue) Reset() {
	*x = StatValue{}
	mi := &file_proto_analytics_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StatValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatValue) ProtoMessage() {}

func (x *StatValue) ProtoReflect() protoreflect.Message {
	mi := &file_proto_analytics_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatValue.ProtoReflect.Descriptor instead.
func (*StatValue) Descriptor() ([]byte, []int) {
	return file_proto_analytics_proto_rawDescGZIP(), []int{1}
}

func (x *StatValue) GetValue() int64 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *StatValue) GetTrend() string {
	if x != nil {
		return x.Trend
	}
	return ""
}

func (x *StatValue) GetTrendValue() string {
	if x != nil {
		return x.TrendValue
	}
	return ""
}

func (x *StatValue) GetPreviousPeriodValue() int64 {
	if x != nil {
		return x.PreviousPeriodValue
	}
	return 0
}

type OverviewResponse struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	TotalUsers     *StatValue             `protobuf:"bytes,1,opt,name=total_users,json=totalUsers,proto3" json:"total_users,omitempty"`
	TotalCourses   *StatValue             `protobuf:"bytes,2,opt,name=total_courses,json=totalCourses,proto3" json:"total_courses,omitempty"`
	TotalThreads   *StatValue             `protobuf:"bytes,3,opt,name=total_threads,json=totalThreads,proto3" json:"total_threads,omitempty"`
	ActiveStudents *StatValue             `protobuf:"bytes,4,opt,name=active_students,json=activeStudents,proto3" json:"active_students,omitempty"`
	Period         string                 `protobuf:"bytes,5,opt,name=period,proto3" json:"period,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *OverviewResponse) Reset() {
	*x = OverviewResponse{}
	mi := &file_proto_analytics_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OverviewResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OverviewResponse) ProtoMessage() {}

func (x *OverviewResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_analytics_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OverviewResponse.ProtoReflect.Descriptor instead.
func (*OverviewResponse) Descriptor() ([]byte, []int) {
	return file_proto_analytics_proto_rawDescGZIP(), []int{2}
}

func (x *OverviewResponse) GetTotalUsers() *StatValue {
	if x != nil {
		return x.TotalUsers
	}
	return nil
}

func (x *OverviewResponse) GetTotalCourses() *StatValue {
	if x != nil {
		return x.TotalCourses
	}
	return nil
}

func (x *OverviewResponse) GetTotalThreads() *StatValue {
	if x != nil {
		return x.TotalThreads
	}
	return nil
}

func (x *OverviewResponse) GetActiveStudents() *StatValue {
	if x != nil {
		return x.ActiveStudents
	}
	return nil
}

func (x *OverviewResponse) GetPeriod() string {
	if x != nil {
		return x.Period
	}
	return ""
}

// Users Timeline Request/Response
type UsersTimelineRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Period        string                 `protobuf:"bytes,1,opt,name=period,proto3" json:"period,omitempty"`           // "7d", "30d", "90d", "1y"
	Granularity   string                 `protobuf:"bytes,2,opt,name=granularity,proto3" json:"granularity,omitempty"` // "day", "week", "month"
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UsersTimelineRequest) Reset() {
	*x = UsersTimelineRequest{}
	mi := &file_proto_analytics_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UsersTimelineRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UsersTimelineRequest) ProtoMessage() {}

func (x *UsersTimelineRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_analytics_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UsersTimelineRequest.ProtoReflect.Descriptor instead.
func (*UsersTimelineRequest) Descriptor() ([]byte, []int) {
	return file_proto_analytics_proto_rawDescGZIP(), []int{3}
}

func (x *UsersTimelineRequest) GetPeriod() string {
	if x != nil {
		return x.Period
	}
	return ""
}

func (x *UsersTimelineRequest) GetGranularity() string {
	if x != nil {
		return x.Granularity
	}
	return ""
}

type TimelinePoint struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Date          string                 `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	TotalUsers    int64                  `protobuf:"varint,2,opt,name=total_users,json=totalUsers,proto3" json:"total_users,omitempty"`
	NewUsers      int64                  `protobuf:"varint,3,opt,name=new_users,json=newUsers,proto3" json:"new_users,omitempty"`
	ActiveUsers   int64                  `protobuf:"varint,4,opt,name=active_users,json=activeUsers,proto3" json:"active_users,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TimelinePoint) Reset() {
	*x = TimelinePoint{}
	mi := &file_proto_analytics_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TimelinePoint) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimelinePoint) ProtoMessage() {}

func (x *TimelinePoint) ProtoReflect() protoreflect.Message {
	mi := &file_proto_analytics_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimelinePoint.ProtoReflect.Descriptor instead.
func (*TimelinePoint) Descriptor() ([]byte, []int) {
	return file_proto_analytics_proto_rawDescGZIP(), []int{4}
}

func (x *TimelinePoint) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *TimelinePoint) GetTotalUsers() int64 {
	if x != nil {
		return x.TotalUsers
	}
	return 0
}

func (x *TimelinePoint) GetNewUsers() int64 {
	if x != nil {
		return x.NewUsers
	}
	return 0
}

func (x *TimelinePoint) GetActiveUsers() int64 {
	if x != nil {
		return x.ActiveUsers
	}
	return 0
}

type UsersTimelineResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Timeline      []*TimelinePoint       `protobuf:"bytes,1,rep,name=timeline,proto3" json:"timeline,omitempty"`
	Comparison    []*TimelinePoint       `protobuf:"bytes,2,rep,name=comparison,proto3" json:"comparison,omitempty"` // previous period
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UsersTimelineResponse) Reset() {
	*x = UsersTimelineResponse{}
	mi := &file_proto_analytics_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UsersTimelineResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UsersTimelineResponse) ProtoMessage() {}

func (x *UsersTimelineResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_analytics_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UsersTimelineResponse.ProtoReflect.Descriptor instead.
func (*UsersTimelineResponse) Descriptor() ([]byte, []int) {
	return file_proto_analytics_proto_rawDescGZIP(), []int{5}
}

func (x *UsersTimelineResponse) GetTimeline() []*TimelinePoint {
	if x != nil {
		return x.Timeline
	}
	return nil
}

func (x *UsersTimelineResponse) GetComparison() []*TimelinePoint {
	if x != nil {
		return x.Comparison
	}
	return nil
}

// User Demographics Request/Response
type UserDemographicsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserDemographicsRequest) Reset() {
	*x = UserDemographicsRequest{}
	mi := &file_proto_analytics_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserDemographicsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserDemographicsRequest) ProtoMessage() {}

func (x *UserDemographicsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_analytics_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserDemographicsRequest.ProtoReflect.Descriptor instead.
func (*UserDemographicsRequest) Descriptor() ([]byte, []int) {
	return file_proto_analytics_proto_rawDescGZIP(), []int{6}
}

type DemographicItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Value         float64                `protobuf:"fixed64,2,opt,name=value,proto3" json:"value,omitempty"`
	Percentage    float64                `protobuf:"fixed64,3,opt,name=percentage,proto3" json:"percentage,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DemographicItem) Reset() {
	*x = DemographicItem{}
	mi := &file_proto_analytics_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DemographicItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DemographicItem) ProtoMessage() {}

func (x *DemographicItem) ProtoReflect() protoreflect.Message {
	mi := &file_proto_analytics_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DemographicItem.ProtoReflect.Descriptor instead.
func (*DemographicItem) Descriptor() ([]byte, []int) {
	return file_proto_analytics_proto_rawDescGZIP(), []int{7}
}

func (x *DemographicItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DemographicItem) GetValue() float64 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *DemographicItem) GetPercentage() float64 {
	if x != nil {
		return x.Percentage
	}
	return 0
}

type UserDemographicsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Devices       []*DemographicItem     `protobuf:"bytes,1,rep,name=devices,proto3" json:"devices,omitempty"`
	Locations     []*DemographicItem     `protobuf:"bytes,2,rep,name=locations,proto3" json:"locations,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserDemographicsResponse) Reset() {
	*x = UserDemographicsResponse{}
	mi := &file_proto_analytics_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserDemographicsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserDemographicsResponse) ProtoMessage() {}

func (x *UserDemographicsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_analytics_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserDemographicsResponse.ProtoReflect.Descriptor instead.
func (*UserDemographicsResponse) Descriptor() ([]byte, []int) {
	return file_proto_analytics_proto_rawDescGZIP(), []int{8}
}

func (x *UserDemographicsResponse) GetDevices() []*DemographicItem {
	if x != nil {
		return x.Devices
	}
	return nil
}

func (x *UserDemographicsResponse) GetLocations() []*DemographicItem {
	if x != nil {
		return x.Locations
	}
	return nil
}

// Recent Activities Request/Response
type RecentActivitiesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Limit         int32                  `protobuf:"varint,1,opt,name=limit,proto3" json:"limit,omitempty"`
	Type          string                 `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"` // "enrollment", "assignment", "login", "all"
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RecentActivitiesRequest) Reset() {
	*x = RecentActivitiesRequest{}
	mi := &file_proto_analytics_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RecentActivitiesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecentActivitiesRequest) ProtoMessage() {}

func (x *RecentActivitiesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_analytics_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecentActivitiesRequest.ProtoReflect.Descriptor instead.
func (*RecentActivitiesRequest) Descriptor() ([]byte, []int) {
	return file_proto_analytics_proto_rawDescGZIP(), []int{9}
}

func (x *RecentActivitiesRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *RecentActivitiesRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

type ActivityUser struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Email         string                 `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActivityUser) Reset() {
	*x = ActivityUser{}
	mi := &file_proto_analytics_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActivityUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityUser) ProtoMessage() {}

func (x *ActivityUser) ProtoReflect() protoreflect.Message {
	mi := &file_proto_analytics_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityUser.ProtoReflect.Descriptor instead.
func (*ActivityUser) Descriptor() ([]byte, []int) {
	return file_proto_analytics_proto_rawDescGZIP(), []int{10}
}

func (x *ActivityUser) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ActivityUser) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ActivityUser) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

type ActivityCourse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Title         string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActivityCourse) Reset() {
	*x = ActivityCourse{}
	mi := &file_proto_analytics_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActivityCourse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityCourse) ProtoMessage() {}

func (x *ActivityCourse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_analytics_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityCourse.ProtoReflect.Descriptor instead.
func (*ActivityCourse) Descriptor() ([]byte, []int) {
	return file_proto_analytics_proto_rawDescGZIP(), []int{11}
}

func (x *ActivityCourse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ActivityCourse) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

type ActivityAssignment struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Title         string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActivityAssignment) Reset() {
	*x = ActivityAssignment{}
	mi := &file_proto_analytics_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActivityAssignment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityAssignment) ProtoMessage() {}

func (x *ActivityAssignment) ProtoReflect() protoreflect.Message {
	mi := &file_proto_analytics_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityAssignment.ProtoReflect.Descriptor instead.
func (*ActivityAssignment) Descriptor() ([]byte, []int) {
	return file_proto_analytics_proto_rawDescGZIP(), []int{12}
}

func (x *ActivityAssignment) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ActivityAssignment) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

type Activity struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Type          string                 `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	User          *ActivityUser          `protobuf:"bytes,3,opt,name=user,proto3" json:"user,omitempty"`
	Course        *ActivityCourse        `protobuf:"bytes,4,opt,name=course,proto3" json:"course,omitempty"`
	Assignment    *ActivityAssignment    `protobuf:"bytes,5,opt,name=assignment,proto3" json:"assignment,omitempty"`
	Date          *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=date,proto3" json:"date,omitempty"`
	Status        string                 `protobuf:"bytes,7,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Activity) Reset() {
	*x = Activity{}
	mi := &file_proto_analytics_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Activity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Activity) ProtoMessage() {}

func (x *Activity) ProtoReflect() protoreflect.Message {
	mi := &file_proto_analytics_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Activity.ProtoReflect.Descriptor instead.
func (*Activity) Descriptor() ([]byte, []int) {
	return file_proto_analytics_proto_rawDescGZIP(), []int{13}
}

func (x *Activity) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Activity) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Activity) GetUser() *ActivityUser {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *Activity) GetCourse() *ActivityCourse {
	if x != nil {
		return x.Course
	}
	return nil
}

func (x *Activity) GetAssignment() *ActivityAssignment {
	if x != nil {
		return x.Assignment
	}
	return nil
}

func (x *Activity) GetDate() *timestamppb.Timestamp {
	if x != nil {
		return x.Date
	}
	return nil
}

func (x *Activity) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type RecentActivitiesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Activities    []*Activity            `protobuf:"bytes,1,rep,name=activities,proto3" json:"activities,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RecentActivitiesResponse) Reset() {
	*x = RecentActivitiesResponse{}
	mi := &file_proto_analytics_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RecentActivitiesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecentActivitiesResponse) ProtoMessage() {}

func (x *RecentActivitiesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_analytics_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecentActivitiesResponse.ProtoReflect.Descriptor instead.
func (*RecentActivitiesResponse) Descriptor() ([]byte, []int) {
	return file_proto_analytics_proto_rawDescGZIP(), []int{14}
}

func (x *RecentActivitiesResponse) GetActivities() []*Activity {
	if x != nil {
		return x.Activities
	}
	return nil
}

// Detailed Stats Request/Response
type DetailedStatsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Period        string                 `protobuf:"bytes,1,opt,name=period,proto3" json:"period,omitempty"`
	Metrics       []string               `protobuf:"bytes,2,rep,name=metrics,proto3" json:"metrics,omitempty"` // "revenue", "activity", "performance"
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DetailedStatsRequest) Reset() {
	*x = DetailedStatsRequest{}
	mi := &file_proto_analytics_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DetailedStatsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetailedStatsRequest) ProtoMessage() {}

func (x *DetailedStatsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_analytics_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetailedStatsRequest.ProtoReflect.Descriptor instead.
func (*DetailedStatsRequest) Descriptor() ([]byte, []int) {
	return file_proto_analytics_proto_rawDescGZIP(), []int{15}
}

func (x *DetailedStatsRequest) GetPeriod() string {
	if x != nil {
		return x.Period
	}
	return ""
}

func (x *DetailedStatsRequest) GetMetrics() []string {
	if x != nil {
		return x.Metrics
	}
	return nil
}

type CourseEnrollmentPoint struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Date          string                 `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	Enrollments   int64                  `protobuf:"varint,2,opt,name=enrollments,proto3" json:"enrollments,omitempty"`
	Completions   int64                  `protobuf:"varint,3,opt,name=completions,proto3" json:"completions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CourseEnrollmentPoint) Reset() {
	*x = CourseEnrollmentPoint{}
	mi := &file_proto_analytics_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CourseEnrollmentPoint) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CourseEnrollmentPoint) ProtoMessage() {}

func (x *CourseEnrollmentPoint) ProtoReflect() protoreflect.Message {
	mi := &file_proto_analytics_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CourseEnrollmentPoint.ProtoReflect.Descriptor instead.
func (*CourseEnrollmentPoint) Descriptor() ([]byte, []int) {
	return file_proto_analytics_proto_rawDescGZIP(), []int{16}
}

func (x *CourseEnrollmentPoint) GetDate() string {
	if x != nil {
		return x.Date
	}
	return ""
}

func (x *CourseEnrollmentPoint) GetEnrollments() int64 {
	if x != nil {
		return x.Enrollments
	}
	return 0
}

func (x *CourseEnrollmentPoint) GetCompletions() int64 {
	if x != nil {
		return x.Completions
	}
	return 0
}

type CourseEnrollments struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Timeline      []*CourseEnrollmentPoint `protobuf:"bytes,1,rep,name=timeline,proto3" json:"timeline,omitempty"`
	Comparison    []*CourseEnrollmentPoint `protobuf:"bytes,2,rep,name=comparison,proto3" json:"comparison,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CourseEnrollments) Reset() {
	*x = CourseEnrollments{}
	mi := &file_proto_analytics_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CourseEnrollments) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CourseEnrollments) ProtoMessage() {}

func (x *CourseEnrollments) ProtoReflect() protoreflect.Message {
	mi := &file_proto_analytics_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CourseEnrollments.ProtoReflect.Descriptor instead.
func (*CourseEnrollments) Descriptor() ([]byte, []int) {
	return file_proto_analytics_proto_rawDescGZIP(), []int{17}
}

func (x *CourseEnrollments) GetTimeline() []*CourseEnrollmentPoint {
	if x != nil {
		return x.Timeline
	}
	return nil
}

func (x *CourseEnrollments) GetComparison() []*CourseEnrollmentPoint {
	if x != nil {
		return x.Comparison
	}
	return nil
}

type UserActivityDay struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Day           string                 `protobuf:"bytes,1,opt,name=day,proto3" json:"day,omitempty"`
	ActiveUsers   int64                  `protobuf:"varint,2,opt,name=active_users,json=activeUsers,proto3" json:"active_users,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserActivityDay) Reset() {
	*x = UserActivityDay{}
	mi := &file_proto_analytics_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserActivityDay) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserActivityDay) ProtoMessage() {}

func (x *UserActivityDay) ProtoReflect() protoreflect.Message {
	mi := &file_proto_analytics_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserActivityDay.ProtoReflect.Descriptor instead.
func (*UserActivityDay) Descriptor() ([]byte, []int) {
	return file_proto_analytics_proto_rawDescGZIP(), []int{18}
}

func (x *UserActivityDay) GetDay() string {
	if x != nil {
		return x.Day
	}
	return ""
}

func (x *UserActivityDay) GetActiveUsers() int64 {
	if x != nil {
		return x.ActiveUsers
	}
	return 0
}

type UserActivity struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Daily         []*UserActivityDay     `protobuf:"bytes,1,rep,name=daily,proto3" json:"daily,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserActivity) Reset() {
	*x = UserActivity{}
	mi := &file_proto_analytics_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserActivity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserActivity) ProtoMessage() {}

func (x *UserActivity) ProtoReflect() protoreflect.Message {
	mi := &file_proto_analytics_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserActivity.ProtoReflect.Descriptor instead.
func (*UserActivity) Descriptor() ([]byte, []int) {
	return file_proto_analytics_proto_rawDescGZIP(), []int{19}
}

func (x *UserActivity) GetDaily() []*UserActivityDay {
	if x != nil {
		return x.Daily
	}
	return nil
}

type TrafficSource struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Source        string                 `protobuf:"bytes,1,opt,name=source,proto3" json:"source,omitempty"`
	Value         float64                `protobuf:"fixed64,2,opt,name=value,proto3" json:"value,omitempty"`
	Percentage    float64                `protobuf:"fixed64,3,opt,name=percentage,proto3" json:"percentage,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrafficSource) Reset() {
	*x = TrafficSource{}
	mi := &file_proto_analytics_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrafficSource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrafficSource) ProtoMessage() {}

func (x *TrafficSource) ProtoReflect() protoreflect.Message {
	mi := &file_proto_analytics_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrafficSource.ProtoReflect.Descriptor instead.
func (*TrafficSource) Descriptor() ([]byte, []int) {
	return file_proto_analytics_proto_rawDescGZIP(), []int{20}
}

func (x *TrafficSource) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *TrafficSource) GetValue() float64 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *TrafficSource) GetPercentage() float64 {
	if x != nil {
		return x.Percentage
	}
	return 0
}

type DetailedStatsResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	CourseEnrollments *CourseEnrollments     `protobuf:"bytes,1,opt,name=course_enrollments,json=courseEnrollments,proto3" json:"course_enrollments,omitempty"`
	UserActivity      *UserActivity          `protobuf:"bytes,2,opt,name=user_activity,json=userActivity,proto3" json:"user_activity,omitempty"`
	TrafficSources    []*TrafficSource       `protobuf:"bytes,3,rep,name=traffic_sources,json=trafficSources,proto3" json:"traffic_sources,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *DetailedStatsResponse) Reset() {
	*x = DetailedStatsResponse{}
	mi := &file_proto_analytics_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DetailedStatsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetailedStatsResponse) ProtoMessage() {}

func (x *DetailedStatsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_analytics_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetailedStatsResponse.ProtoReflect.Descriptor instead.
func (*DetailedStatsResponse) Descriptor() ([]byte, []int) {
	return file_proto_analytics_proto_rawDescGZIP(), []int{21}
}

func (x *DetailedStatsResponse) GetCourseEnrollments() *CourseEnrollments {
	if x != nil {
		return x.CourseEnrollments
	}
	return nil
}

func (x *DetailedStatsResponse) GetUserActivity() *UserActivity {
	if x != nil {
		return x.UserActivity
	}
	return nil
}

func (x *DetailedStatsResponse) GetTrafficSources() []*TrafficSource {
	if x != nil {
		return x.TrafficSources
	}
	return nil
}

// Performance Metrics Request/Response
type PerformanceMetricsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PerformanceMetricsRequest) Reset() {
	*x = PerformanceMetricsRequest{}
	mi := &file_proto_analytics_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerformanceMetricsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerformanceMetricsRequest) ProtoMessage() {}

func (x *PerformanceMetricsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_analytics_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerformanceMetricsRequest.ProtoReflect.Descriptor instead.
func (*PerformanceMetricsRequest) Descriptor() ([]byte, []int) {
	return file_proto_analytics_proto_rawDescGZIP(), []int{22}
}

type PerformanceMetric struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Value         float64                `protobuf:"fixed64,1,opt,name=value,proto3" json:"value,omitempty"`
	Unit          string                 `protobuf:"bytes,2,opt,name=unit,proto3" json:"unit,omitempty"`
	Trend         string                 `protobuf:"bytes,3,opt,name=trend,proto3" json:"trend,omitempty"`
	Change        string                 `protobuf:"bytes,4,opt,name=change,proto3" json:"change,omitempty"`
	Description   string                 `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PerformanceMetric) Reset() {
	*x = PerformanceMetric{}
	mi := &file_proto_analytics_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerformanceMetric) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerformanceMetric) ProtoMessage() {}

func (x *PerformanceMetric) ProtoReflect() protoreflect.Message {
	mi := &file_proto_analytics_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerformanceMetric.ProtoReflect.Descriptor instead.
func (*PerformanceMetric) Descriptor() ([]byte, []int) {
	return file_proto_analytics_proto_rawDescGZIP(), []int{23}
}

func (x *PerformanceMetric) GetValue() float64 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *PerformanceMetric) GetUnit() string {
	if x != nil {
		return x.Unit
	}
	return ""
}

func (x *PerformanceMetric) GetTrend() string {
	if x != nil {
		return x.Trend
	}
	return ""
}

func (x *PerformanceMetric) GetChange() string {
	if x != nil {
		return x.Change
	}
	return ""
}

func (x *PerformanceMetric) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type PerformanceMetricsResponse struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	CourseCompletionRate   *PerformanceMetric     `protobuf:"bytes,1,opt,name=course_completion_rate,json=courseCompletionRate,proto3" json:"course_completion_rate,omitempty"`
	AverageGrade           *PerformanceMetric     `protobuf:"bytes,2,opt,name=average_grade,json=averageGrade,proto3" json:"average_grade,omitempty"`
	AttendanceRate         *PerformanceMetric     `protobuf:"bytes,3,opt,name=attendance_rate,json=attendanceRate,proto3" json:"attendance_rate,omitempty"`
	AverageSessionDuration *PerformanceMetric     `protobuf:"bytes,4,opt,name=average_session_duration,json=averageSessionDuration,proto3" json:"average_session_duration,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *PerformanceMetricsResponse) Reset() {
	*x = PerformanceMetricsResponse{}
	mi := &file_proto_analytics_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerformanceMetricsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerformanceMetricsResponse) ProtoMessage() {}

func (x *PerformanceMetricsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_analytics_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerformanceMetricsResponse.ProtoReflect.Descriptor instead.
func (*PerformanceMetricsResponse) Descriptor() ([]byte, []int) {
	return file_proto_analytics_proto_rawDescGZIP(), []int{24}
}

func (x *PerformanceMetricsResponse) GetCourseCompletionRate() *PerformanceMetric {
	if x != nil {
		return x.CourseCompletionRate
	}
	return nil
}

func (x *PerformanceMetricsResponse) GetAverageGrade() *PerformanceMetric {
	if x != nil {
		return x.AverageGrade
	}
	return nil
}

func (x *PerformanceMetricsResponse) GetAttendanceRate() *PerformanceMetric {
	if x != nil {
		return x.AttendanceRate
	}
	return nil
}

func (x *PerformanceMetricsResponse) GetAverageSessionDuration() *PerformanceMetric {
	if x != nil {
		return x.AverageSessionDuration
	}
	return nil
}

var File_proto_analytics_proto protoreflect.FileDescriptor

const file_proto_analytics_proto_rawDesc = "" +
	"\n" +
	"\x15proto/analytics.proto\x12\vanalyticspb\x1a\x1fgoogle/protobuf/timestamp.proto\")\n" +
	"\x0fOverviewRequest\x12\x16\n" +
	"\x06period\x18\x01 \x01(\tR\x06period\"\x8c\x01\n" +
	"\tStatValue\x12\x14\n" +
	"\x05value\x18\x01 \x01(\x03R\x05value\x12\x14\n" +
	"\x05trend\x18\x02 \x01(\tR\x05trend\x12\x1f\n" +
	"\vtrend_value\x18\x03 \x01(\tR\n" +
	"trendValue\x122\n" +
	"\x15previous_period_value\x18\x04 \x01(\x03R\x13previousPeriodValue\"\x9e\x02\n" +
	"\x10OverviewResponse\x127\n" +
	"\vtotal_users\x18\x01 \x01(\v2\x16.analyticspb.StatValueR\n" +
	"totalUsers\x12;\n" +
	"\rtotal_courses\x18\x02 \x01(\v2\x16.analyticspb.StatValueR\ftotalCourses\x12;\n" +
	"\rtotal_threads\x18\x03 \x01(\v2\x16.analyticspb.StatValueR\ftotalThreads\x12?\n" +
	"\x0factive_students\x18\x04 \x01(\v2\x16.analyticspb.StatValueR\x0eactiveStudents\x12\x16\n" +
	"\x06period\x18\x05 \x01(\tR\x06period\"P\n" +
	"\x14UsersTimelineRequest\x12\x16\n" +
	"\x06period\x18\x01 \x01(\tR\x06period\x12 \n" +
	"\vgranularity\x18\x02 \x01(\tR\vgranularity\"\x84\x01\n" +
	"\rTimelinePoint\x12\x12\n" +
	"\x04date\x18\x01 \x01(\tR\x04date\x12\x1f\n" +
	"\vtotal_users\x18\x02 \x01(\x03R\n" +
	"totalUsers\x12\x1b\n" +
	"\tnew_users\x18\x03 \x01(\x03R\bnewUsers\x12!\n" +
	"\factive_users\x18\x04 \x01(\x03R\vactiveUsers\"\x8b\x01\n" +
	"\x15UsersTimelineResponse\x126\n" +
	"\btimeline\x18\x01 \x03(\v2\x1a.analyticspb.TimelinePointR\btimeline\x12:\n" +
	"\n" +
	"comparison\x18\x02 \x03(\v2\x1a.analyticspb.TimelinePointR\n" +
	"comparison\"\x19\n" +
	"\x17UserDemographicsRequest\"[\n" +
	"\x0fDemographicItem\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x01R\x05value\x12\x1e\n" +
	"\n" +
	"percentage\x18\x03 \x01(\x01R\n" +
	"percentage\"\x8e\x01\n" +
	"\x18UserDemographicsResponse\x126\n" +
	"\adevices\x18\x01 \x03(\v2\x1c.analyticspb.DemographicItemR\adevices\x12:\n" +
	"\tlocations\x18\x02 \x03(\v2\x1c.analyticspb.DemographicItemR\tlocations\"C\n" +
	"\x17RecentActivitiesRequest\x12\x14\n" +
	"\x05limit\x18\x01 \x01(\x05R\x05limit\x12\x12\n" +
	"\x04type\x18\x02 \x01(\tR\x04type\"H\n" +
	"\fActivityUser\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x14\n" +
	"\x05email\x18\x03 \x01(\tR\x05email\"6\n" +
	"\x0eActivityCourse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x14\n" +
	"\x05title\x18\x02 \x01(\tR\x05title\":\n" +
	"\x12ActivityAssignment\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x14\n" +
	"\x05title\x18\x02 \x01(\tR\x05title\"\x9b\x02\n" +
	"\bActivity\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04type\x18\x02 \x01(\tR\x04type\x12-\n" +
	"\x04user\x18\x03 \x01(\v2\x19.analyticspb.ActivityUserR\x04user\x123\n" +
	"\x06course\x18\x04 \x01(\v2\x1b.analyticspb.ActivityCourseR\x06course\x12?\n" +
	"\n" +
	"assignment\x18\x05 \x01(\v2\x1f.analyticspb.ActivityAssignmentR\n" +
	"assignment\x12.\n" +
	"\x04date\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\x04date\x12\x16\n" +
	"\x06status\x18\a \x01(\tR\x06status\"Q\n" +
	"\x18RecentActivitiesResponse\x125\n" +
	"\n" +
	"activities\x18\x01 \x03(\v2\x15.analyticspb.ActivityR\n" +
	"activities\"H\n" +
	"\x14DetailedStatsRequest\x12\x16\n" +
	"\x06period\x18\x01 \x01(\tR\x06period\x12\x18\n" +
	"\ametrics\x18\x02 \x03(\tR\ametrics\"o\n" +
	"\x15CourseEnrollmentPoint\x12\x12\n" +
	"\x04date\x18\x01 \x01(\tR\x04date\x12 \n" +
	"\venrollments\x18\x02 \x01(\x03R\venrollments\x12 \n" +
	"\vcompletions\x18\x03 \x01(\x03R\vcompletions\"\x97\x01\n" +
	"\x11CourseEnrollments\x12>\n" +
	"\btimeline\x18\x01 \x03(\v2\".analyticspb.CourseEnrollmentPointR\btimeline\x12B\n" +
	"\n" +
	"comparison\x18\x02 \x03(\v2\".analyticspb.CourseEnrollmentPointR\n" +
	"comparison\"F\n" +
	"\x0fUserActivityDay\x12\x10\n" +
	"\x03day\x18\x01 \x01(\tR\x03day\x12!\n" +
	"\factive_users\x18\x02 \x01(\x03R\vactiveUsers\"B\n" +
	"\fUserActivity\x122\n" +
	"\x05daily\x18\x01 \x03(\v2\x1c.analyticspb.UserActivityDayR\x05daily\"]\n" +
	"\rTrafficSource\x12\x16\n" +
	"\x06source\x18\x01 \x01(\tR\x06source\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x01R\x05value\x12\x1e\n" +
	"\n" +
	"percentage\x18\x03 \x01(\x01R\n" +
	"percentage\"\xeb\x01\n" +
	"\x15DetailedStatsResponse\x12M\n" +
	"\x12course_enrollments\x18\x01 \x01(\v2\x1e.analyticspb.CourseEnrollmentsR\x11courseEnrollments\x12>\n" +
	"\ruser_activity\x18\x02 \x01(\v2\x19.analyticspb.UserActivityR\fuserActivity\x12C\n" +
	"\x0ftraffic_sources\x18\x03 \x03(\v2\x1a.analyticspb.TrafficSourceR\x0etrafficSources\"\x1b\n" +
	"\x19PerformanceMetricsRequest\"\x8d\x01\n" +
	"\x11PerformanceMetric\x12\x14\n" +
	"\x05value\x18\x01 \x01(\x01R\x05value\x12\x12\n" +
	"\x04unit\x18\x02 \x01(\tR\x04unit\x12\x14\n" +
	"\x05trend\x18\x03 \x01(\tR\x05trend\x12\x16\n" +
	"\x06change\x18\x04 \x01(\tR\x06change\x12 \n" +
	"\vdescription\x18\x05 \x01(\tR\vdescription\"\xda\x02\n" +
	"\x1aPerformanceMetricsResponse\x12T\n" +
	"\x16course_completion_rate\x18\x01 \x01(\v2\x1e.analyticspb.PerformanceMetricR\x14courseCompletionRate\x12C\n" +
	"\raverage_grade\x18\x02 \x01(\v2\x1e.analyticspb.PerformanceMetricR\faverageGrade\x12G\n" +
	"\x0fattendance_rate\x18\x03 \x01(\v2\x1e.analyticspb.PerformanceMetricR\x0eattendanceRate\x12X\n" +
	"\x18average_session_duration\x18\x04 \x01(\v2\x1e.analyticspb.PerformanceMetricR\x16averageSessionDuration2\xd2\x04\n" +
	"\x10AnalyticsService\x12L\n" +
	"\vGetOverview\x12\x1c.analyticspb.OverviewRequest\x1a\x1d.analyticspb.OverviewResponse\"\x00\x12[\n" +
	"\x10GetUsersTimeline\x12!.analyticspb.UsersTimelineRequest\x1a\".analyticspb.UsersTimelineResponse\"\x00\x12d\n" +
	"\x13GetUserDemographics\x12$.analyticspb.UserDemographicsRequest\x1a%.analyticspb.UserDemographicsResponse\"\x00\x12d\n" +
	"\x13GetRecentActivities\x12$.analyticspb.RecentActivitiesRequest\x1a%.analyticspb.RecentActivitiesResponse\"\x00\x12[\n" +
	"\x10GetDetailedStats\x12!.analyticspb.DetailedStatsRequest\x1a\".analyticspb.DetailedStatsResponse\"\x00\x12j\n" +
	"\x15GetPerformanceMetrics\x12&.analyticspb.PerformanceMetricsRequest\x1a'.analyticspb.PerformanceMetricsResponse\"\x00BNZLgithub.com/olzzhas/edunite-server/analytics_service/pb/analytics;analyticspbb\x06proto3"

var (
	file_proto_analytics_proto_rawDescOnce sync.Once
	file_proto_analytics_proto_rawDescData []byte
)

func file_proto_analytics_proto_rawDescGZIP() []byte {
	file_proto_analytics_proto_rawDescOnce.Do(func() {
		file_proto_analytics_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_analytics_proto_rawDesc), len(file_proto_analytics_proto_rawDesc)))
	})
	return file_proto_analytics_proto_rawDescData
}

var file_proto_analytics_proto_msgTypes = make([]protoimpl.MessageInfo, 25)
var file_proto_analytics_proto_goTypes = []any{
	(*OverviewRequest)(nil),            // 0: analyticspb.OverviewRequest
	(*StatValue)(nil),                  // 1: analyticspb.StatValue
	(*OverviewResponse)(nil),           // 2: analyticspb.OverviewResponse
	(*UsersTimelineRequest)(nil),       // 3: analyticspb.UsersTimelineRequest
	(*TimelinePoint)(nil),              // 4: analyticspb.TimelinePoint
	(*UsersTimelineResponse)(nil),      // 5: analyticspb.UsersTimelineResponse
	(*UserDemographicsRequest)(nil),    // 6: analyticspb.UserDemographicsRequest
	(*DemographicItem)(nil),            // 7: analyticspb.DemographicItem
	(*UserDemographicsResponse)(nil),   // 8: analyticspb.UserDemographicsResponse
	(*RecentActivitiesRequest)(nil),    // 9: analyticspb.RecentActivitiesRequest
	(*ActivityUser)(nil),               // 10: analyticspb.ActivityUser
	(*ActivityCourse)(nil),             // 11: analyticspb.ActivityCourse
	(*ActivityAssignment)(nil),         // 12: analyticspb.ActivityAssignment
	(*Activity)(nil),                   // 13: analyticspb.Activity
	(*RecentActivitiesResponse)(nil),   // 14: analyticspb.RecentActivitiesResponse
	(*DetailedStatsRequest)(nil),       // 15: analyticspb.DetailedStatsRequest
	(*CourseEnrollmentPoint)(nil),      // 16: analyticspb.CourseEnrollmentPoint
	(*CourseEnrollments)(nil),          // 17: analyticspb.CourseEnrollments
	(*UserActivityDay)(nil),            // 18: analyticspb.UserActivityDay
	(*UserActivity)(nil),               // 19: analyticspb.UserActivity
	(*TrafficSource)(nil),              // 20: analyticspb.TrafficSource
	(*DetailedStatsResponse)(nil),      // 21: analyticspb.DetailedStatsResponse
	(*PerformanceMetricsRequest)(nil),  // 22: analyticspb.PerformanceMetricsRequest
	(*PerformanceMetric)(nil),          // 23: analyticspb.PerformanceMetric
	(*PerformanceMetricsResponse)(nil), // 24: analyticspb.PerformanceMetricsResponse
	(*timestamppb.Timestamp)(nil),      // 25: google.protobuf.Timestamp
}
var file_proto_analytics_proto_depIdxs = []int32{
	1,  // 0: analyticspb.OverviewResponse.total_users:type_name -> analyticspb.StatValue
	1,  // 1: analyticspb.OverviewResponse.total_courses:type_name -> analyticspb.StatValue
	1,  // 2: analyticspb.OverviewResponse.total_threads:type_name -> analyticspb.StatValue
	1,  // 3: analyticspb.OverviewResponse.active_students:type_name -> analyticspb.StatValue
	4,  // 4: analyticspb.UsersTimelineResponse.timeline:type_name -> analyticspb.TimelinePoint
	4,  // 5: analyticspb.UsersTimelineResponse.comparison:type_name -> analyticspb.TimelinePoint
	7,  // 6: analyticspb.UserDemographicsResponse.devices:type_name -> analyticspb.DemographicItem
	7,  // 7: analyticspb.UserDemographicsResponse.locations:type_name -> analyticspb.DemographicItem
	10, // 8: analyticspb.Activity.user:type_name -> analyticspb.ActivityUser
	11, // 9: analyticspb.Activity.course:type_name -> analyticspb.ActivityCourse
	12, // 10: analyticspb.Activity.assignment:type_name -> analyticspb.ActivityAssignment
	25, // 11: analyticspb.Activity.date:type_name -> google.protobuf.Timestamp
	13, // 12: analyticspb.RecentActivitiesResponse.activities:type_name -> analyticspb.Activity
	16, // 13: analyticspb.CourseEnrollments.timeline:type_name -> analyticspb.CourseEnrollmentPoint
	16, // 14: analyticspb.CourseEnrollments.comparison:type_name -> analyticspb.CourseEnrollmentPoint
	18, // 15: analyticspb.UserActivity.daily:type_name -> analyticspb.UserActivityDay
	17, // 16: analyticspb.DetailedStatsResponse.course_enrollments:type_name -> analyticspb.CourseEnrollments
	19, // 17: analyticspb.DetailedStatsResponse.user_activity:type_name -> analyticspb.UserActivity
	20, // 18: analyticspb.DetailedStatsResponse.traffic_sources:type_name -> analyticspb.TrafficSource
	23, // 19: analyticspb.PerformanceMetricsResponse.course_completion_rate:type_name -> analyticspb.PerformanceMetric
	23, // 20: analyticspb.PerformanceMetricsResponse.average_grade:type_name -> analyticspb.PerformanceMetric
	23, // 21: analyticspb.PerformanceMetricsResponse.attendance_rate:type_name -> analyticspb.PerformanceMetric
	23, // 22: analyticspb.PerformanceMetricsResponse.average_session_duration:type_name -> analyticspb.PerformanceMetric
	0,  // 23: analyticspb.AnalyticsService.GetOverview:input_type -> analyticspb.OverviewRequest
	3,  // 24: analyticspb.AnalyticsService.GetUsersTimeline:input_type -> analyticspb.UsersTimelineRequest
	6,  // 25: analyticspb.AnalyticsService.GetUserDemographics:input_type -> analyticspb.UserDemographicsRequest
	9,  // 26: analyticspb.AnalyticsService.GetRecentActivities:input_type -> analyticspb.RecentActivitiesRequest
	15, // 27: analyticspb.AnalyticsService.GetDetailedStats:input_type -> analyticspb.DetailedStatsRequest
	22, // 28: analyticspb.AnalyticsService.GetPerformanceMetrics:input_type -> analyticspb.PerformanceMetricsRequest
	2,  // 29: analyticspb.AnalyticsService.GetOverview:output_type -> analyticspb.OverviewResponse
	5,  // 30: analyticspb.AnalyticsService.GetUsersTimeline:output_type -> analyticspb.UsersTimelineResponse
	8,  // 31: analyticspb.AnalyticsService.GetUserDemographics:output_type -> analyticspb.UserDemographicsResponse
	14, // 32: analyticspb.AnalyticsService.GetRecentActivities:output_type -> analyticspb.RecentActivitiesResponse
	21, // 33: analyticspb.AnalyticsService.GetDetailedStats:output_type -> analyticspb.DetailedStatsResponse
	24, // 34: analyticspb.AnalyticsService.GetPerformanceMetrics:output_type -> analyticspb.PerformanceMetricsResponse
	29, // [29:35] is the sub-list for method output_type
	23, // [23:29] is the sub-list for method input_type
	23, // [23:23] is the sub-list for extension type_name
	23, // [23:23] is the sub-list for extension extendee
	0,  // [0:23] is the sub-list for field type_name
}

func init() { file_proto_analytics_proto_init() }
func file_proto_analytics_proto_init() {
	if File_proto_analytics_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_analytics_proto_rawDesc), len(file_proto_analytics_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   25,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_analytics_proto_goTypes,
		DependencyIndexes: file_proto_analytics_proto_depIdxs,
		MessageInfos:      file_proto_analytics_proto_msgTypes,
	}.Build()
	File_proto_analytics_proto = out.File
	file_proto_analytics_proto_goTypes = nil
	file_proto_analytics_proto_depIdxs = nil
}
