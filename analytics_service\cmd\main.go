package main

import (
	"log"
	"net"

	"github.com/olzzhas/edunite-server/analytics_service/internal/config"
	"github.com/olzzhas/edunite-server/analytics_service/internal/database"
	"github.com/olzzhas/edunite-server/analytics_service/internal/repository"
	"github.com/olzzhas/edunite-server/analytics_service/internal/service"
	analyticspb "github.com/olzzhas/edunite-server/analytics_service/pb/analytics"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"
)

func main() {
	// Load configuration
	cfg := config.LoadConfig()

	// Connect to database
	db := database.ConnectDB(cfg)
	defer db.Close()

	// Initialize repository
	repo := repository.NewAnalyticsRepository(db)

	// Initialize service
	analyticsService := service.NewAnalyticsService(repo)

	// Create gRPC server
	grpcServer := grpc.NewServer()

	// Register service
	analyticspb.RegisterAnalyticsServiceServer(grpcServer, analyticsService)

	// Enable reflection for development
	reflection.Register(grpcServer)

	// Start listening
	listener, err := net.Listen("tcp", ":"+cfg.Port)
	if err != nil {
		log.Fatalf("Failed to listen on port %s: %v", cfg.Port, err)
	}

	log.Printf("Analytics Service is running on port %s", cfg.Port)
	if err := grpcServer.Serve(listener); err != nil {
		log.Fatalf("Failed to serve gRPC server: %v", err)
	}
}
