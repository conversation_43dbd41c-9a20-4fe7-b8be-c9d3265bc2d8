# Thread Course Grades API

## Overview

Этот документ описывает новый endpoint для получения только названий курсов и финальных оценок для потоков пользователя.

## Endpoint

### Get Thread Course Grades for User

**GET** `/thread/user/{user_id}/course-grades`

Возвращает упрощенную информацию о потоках пользователя - только название курса и финальную оценку.

#### Parameters

- `user_id` (path parameter, required) - ID пользователя

#### Response

**Success Response (200 OK):**

```json
{
  "grades": [
    {
      "thread_id": 1,
      "course_name": "Математический анализ",
      "final_grade": 85.5
    },
    {
      "thread_id": 2,
      "course_name": "Программирование",
      "final_grade": 92.0
    },
    {
      "thread_id": 3,
      "course_name": "Физика",
      "final_grade": 0
    }
  ]
}
```

**Error Responses:**

- `400 Bad Request` - Неверный user_id
- `500 Internal Server Error` - Внутренняя ошибка сервера

#### Example Request

```bash
curl -X GET "http://localhost:8080/thread/user/123/course-grades" \
  -H "Content-Type: application/json"
```

#### Example Response

```json
{
  "grades": [
    {
      "thread_id": 15,
      "course_name": "Базы данных",
      "final_grade": 88.5
    },
    {
      "thread_id": 22,
      "course_name": "Веб-разработка",
      "final_grade": 95.0
    }
  ]
}
```

## Notes

- Если у пользователя нет зарегистрированных потоков, возвращается пустой массив `grades`
- `final_grade` равен 0, если финальная оценка еще не выставлена
- Endpoint возвращает только потоки, на которые пользователь зарегистрирован
- Данные сортируются по ID потока
