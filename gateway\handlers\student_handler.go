package handlers

import (
	"fmt"
	"net/http"
	"sort"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	coursepb "github.com/olzzhas/edunite-server/course_service/pb/course"
	threadpb "github.com/olzzhas/edunite-server/course_service/pb/thread"
	transcriptpb "github.com/olzzhas/edunite-server/course_service/pb/transcript"
	"github.com/olzzhas/edunite-server/gateway/clients"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// StudentHandler handles student-related requests
type StudentHandler struct {
	ThreadClient       *clients.ThreadClient
	ScheduleClient     *clients.ScheduleClient
	SportClient        *clients.ScheduleClient
	BookingClient      *clients.BookingClient
	AssignmentClient   *clients.AssignmentClient
	UserClient         *clients.UserClient
	TranscriptClient   *clients.TranscriptClient
	CourseClient       *clients.CourseClient
	RabbitLogPublisher clients.LogPublisher
}

// NewStudentHandler creates a new StudentHandler
func NewStudentHandler(
	threadClient *clients.ThreadClient,
	scheduleClient *clients.ScheduleClient,
	sportClient *clients.ScheduleClient,
	bookingClient *clients.BookingClient,
	assignmentClient *clients.AssignmentClient,
	userClient *clients.UserClient,
	transcriptClient *clients.TranscriptClient,
	courseClient *clients.CourseClient,
	rabbitLogPublisher clients.LogPublisher,
) *StudentHandler {
	return &StudentHandler{
		ThreadClient:       threadClient,
		ScheduleClient:     scheduleClient,
		SportClient:        sportClient,
		BookingClient:      bookingClient,
		AssignmentClient:   assignmentClient,
		UserClient:         userClient,
		TranscriptClient:   transcriptClient,
		CourseClient:       courseClient,
		RabbitLogPublisher: rabbitLogPublisher,
	}
}

// GetStudentScheduleHandler handles GET /students/:student_id/schedule
// This endpoint returns a comprehensive schedule for a student, including:
// - Thread schedules (regular class times)
// - Sport schedules and bookings
func (h *StudentHandler) GetStudentScheduleHandler(c *gin.Context) {
	// Parse student ID
	studentIDStr := c.Param("user_id")
	studentID, err := strconv.ParseInt(studentIDStr, 10, 64)
	if err != nil || studentID <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid student ID"})
		return
	}

	// Parse optional date range parameters
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")

	var startDate, endDate time.Time
	if startDateStr != "" {
		parsedStartDate, err := time.Parse(time.RFC3339, startDateStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid start_date format, use RFC3339"})
			return
		}
		startDate = parsedStartDate
	} else {
		// Default to today
		startDate = time.Now().Truncate(24 * time.Hour)
	}

	if endDateStr != "" {
		parsedEndDate, err := time.Parse(time.RFC3339, endDateStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid end_date format, use RFC3339"})
			return
		}
		endDate = parsedEndDate
	} else {
		// Default to 7 days from start date
		endDate = startDate.AddDate(0, 0, 7)
	}

	// 1. Get threads the student is registered for
	threads, err := h.ThreadClient.ListThreadsForUser(c.Request.Context(), &threadpb.UserThreadsRequest{
		UserId: studentID,
	})
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error getting threads for student: %v", err),
			"student_schedule",
			map[string]any{"student_id": studentID, "error": err.Error()},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to get student threads"})
		return
	}

	// 2. Get thread schedules for each thread
	var threadSchedules []gin.H
	for _, thread := range threads.GetThreads() {
		schedules, err := h.ThreadClient.ListThreadSchedules(c.Request.Context(), &threadpb.ThreadSchedulesRequest{
			ThreadId: thread.Thread.Id,
		})
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error getting schedules for thread: %v", err),
				"student_schedule",
				map[string]any{"thread_id": thread.Thread.Id, "error": err.Error()},
			)
			continue // Skip this thread but continue with others
		}

		// Convert weekly schedules to actual dates within the range
		for _, schedule := range schedules.GetSchedules() {
			// Generate actual dates for this schedule within the date range
			for date := startDate; date.Before(endDate); date = date.AddDate(0, 0, 1) {
				// Check if this date's day of week matches the schedule
				if int(date.Weekday()) == int(schedule.DayOfWeek)%7 {
					// Parse the start and end times
					startTime, _ := time.Parse("15:04:05", schedule.StartTime)
					endTime, _ := time.Parse("15:04:05", schedule.EndTime)

					// Combine date with time
					scheduleStart := time.Date(
						date.Year(), date.Month(), date.Day(),
						startTime.Hour(), startTime.Minute(), startTime.Second(),
						0, date.Location(),
					)
					scheduleEnd := time.Date(
						date.Year(), date.Month(), date.Day(),
						endTime.Hour(), endTime.Minute(), endTime.Second(),
						0, date.Location(),
					)

					// Get location information
					var location string
					var locationID int64

					// First try to get location from schedule
					location = schedule.Location

					// If location is empty and we have a location_id, try to get location by ID
					if location == "" && schedule.Id > 0 {
						// Get the schedule by ID to check if it has location information
						scheduleResp, err := h.ThreadClient.GetThreadScheduleByID(c.Request.Context(), &threadpb.ThreadScheduleByID{
							ScheduleId: schedule.Id,
						})
						if err == nil && scheduleResp != nil {
							location = scheduleResp.Location

							// If we have a schedule but still no location, try to get location from location service
							if location == "" {
								// Try to get location from the thread service using the schedule ID
								locationFromService, err := h.ThreadClient.GetScheduleLocation(c.Request.Context(), schedule.Id)
								if err == nil && locationFromService != "" {
									location = locationFromService
								}
							}
						}
					}

					// If still no location, provide a default message
					if location == "" {
						location = "No location specified"
					}

					threadSchedules = append(threadSchedules, gin.H{
						"type":        "class",
						"thread_id":   thread.Thread.Id,
						"thread_name": thread.Thread.Title,
						"course_name": thread.Course.Title,
						"start_time":  scheduleStart.Format(time.RFC3339),
						"end_time":    scheduleEnd.Format(time.RFC3339),
						"location":    location,
						"schedule_id": schedule.Id,
						"location_id": locationID,
					})
				}
			}
		}
	}

	// 3. Get sport bookings for the student
	startTimestamp := timestamppb.New(startDate)
	endTimestamp := timestamppb.New(endDate)

	bookings, err := h.BookingClient.ListBookingsByUser(c.Request.Context(), studentID, startTimestamp, endTimestamp)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error getting sport bookings for student: %v", err),
			"student_schedule",
			map[string]any{"student_id": studentID, "error": err.Error()},
		)
		// Continue without sport bookings
	}

	// 4. Convert sport bookings to schedule items
	var sportSchedules []gin.H
	if bookings != nil {
		for _, booking := range bookings.GetBookings() {
			sportSchedules = append(sportSchedules, gin.H{
				"type":        "sport",
				"booking_id":  booking.Id,
				"schedule_id": booking.ScheduleId,
				"start_time":  booking.Schedule.StartTime.AsTime().Format(time.RFC3339),
				"end_time":    booking.Schedule.EndTime.AsTime().Format(time.RFC3339),
				"location":    booking.Schedule.Location,
				"sport_type":  "Sport Activity", // We don't have sport type in the schedule info
				"status":      booking.Status,
			})
		}
	}

	// 5. Combine all schedules and sort by start time
	allSchedules := append(threadSchedules, sportSchedules...)

	// Sort schedules by start time
	sort.Slice(allSchedules, func(i, j int) bool {
		startTime1, _ := time.Parse(time.RFC3339, allSchedules[i]["start_time"].(string))
		startTime2, _ := time.Parse(time.RFC3339, allSchedules[j]["start_time"].(string))
		return startTime1.Before(startTime2)
	})

	c.JSON(http.StatusOK, gin.H{
		"student_id": studentID,
		"start_date": startDate.Format(time.RFC3339),
		"end_date":   endDate.Format(time.RFC3339),
		"schedules":  allSchedules,
	})
}

// ListPendingAssignmentsHandler возвращает все задания, которые студент еще не выполнил (нет сабмишенов)
func (h *StudentHandler) ListPendingAssignmentsHandler(c *gin.Context) {
	// Parse student ID
	studentIDStr := c.Param("user_id")
	studentID, err := strconv.ParseInt(studentIDStr, 10, 64)
	if err != nil || studentID <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid student ID"})
		return
	}

	// Parse thread ID
	threadIDStr := c.Param("thread_id")
	threadID, err := strconv.ParseInt(threadIDStr, 10, 64)
	if err != nil || threadID <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid thread ID"})
		return
	}

	// Get assignments without submissions
	assignments, err := h.AssignmentClient.ListAssignmentsWithoutSubmissionForThread(
		c.Request.Context(),
		threadID,
		studentID,
	)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error getting pending assignments: %v", err),
			"pending_assignments",
			map[string]any{
				"student_id": studentID,
				"thread_id":  threadID,
				"error":      err.Error(),
			},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to get pending assignments"})
		return
	}

	// Format the response
	var formattedAssignments []gin.H
	for _, item := range assignments {
		assignment := item.GetAssignment()

		// Calculate deadline status
		now := time.Now()
		dueDate := assignment.GetDueDate().AsTime()
		daysRemaining := int(dueDate.Sub(now).Hours() / 24)
		isOverdue := now.After(dueDate)

		var statusText string
		if isOverdue {
			statusText = "Overdue"
		} else if daysRemaining == 0 {
			statusText = "Due today"
		} else if daysRemaining == 1 {
			statusText = "Due tomorrow"
		} else {
			statusText = "Due in " + strconv.Itoa(daysRemaining) + " days"
		}

		formattedAssignments = append(formattedAssignments, gin.H{
			"id":                  assignment.GetId(),
			"week_id":             assignment.GetWeekId(),
			"title":               assignment.GetTitle(),
			"description":         assignment.GetDescription(),
			"due_date":            assignment.GetDueDate().AsTime().Format(time.RFC3339),
			"max_points":          assignment.GetMaxPoints(),
			"assignment_group_id": assignment.GetAssignmentGroupId(),
			"type":                assignment.GetType(),
			"created_at":          assignment.GetCreatedAt().AsTime().Format(time.RFC3339),
			"updated_at":          assignment.GetUpdatedAt().AsTime().Format(time.RFC3339),
			"deadline_status": gin.H{
				"is_overdue":     isOverdue,
				"days_remaining": daysRemaining,
				"status_text":    statusText,
			},
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"student_id":  studentID,
		"thread_id":   threadID,
		"count":       len(formattedAssignments),
		"assignments": formattedAssignments,
	})
}

// ListAllPendingAssignmentsHandler возвращает все задания из всех потоков, которые студент еще не выполнил
func (h *StudentHandler) ListAllPendingAssignmentsHandler(c *gin.Context) {
	// Parse student ID
	studentIDStr := c.Param("user_id")
	studentID, err := strconv.ParseInt(studentIDStr, 10, 64)
	if err != nil || studentID <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid student ID"})
		return
	}

	// Get all threads the student is enrolled in
	threadsResp, err := h.ThreadClient.ListThreadsForUser(
		c.Request.Context(),
		&threadpb.UserThreadsRequest{UserId: studentID},
	)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error getting threads for student: %v", err),
			"all_pending_assignments",
			map[string]any{"student_id": studentID, "error": err.Error()},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to get student threads"})
		return
	}

	if len(threadsResp.GetThreads()) == 0 {
		c.JSON(http.StatusOK, gin.H{
			"student_id":  studentID,
			"count":       0,
			"assignments": []any{},
		})
		return
	}

	// For each thread, get assignments without submissions
	var allPendingAssignments []gin.H
	threadInfo := make(map[int64]gin.H)

	for _, threadWithDetails := range threadsResp.GetThreads() {
		thread := threadWithDetails.GetThread()
		course := threadWithDetails.GetCourse()

		// Store thread info for reference
		threadInfo[thread.Id] = gin.H{
			"id":    thread.Id,
			"title": thread.Title,
			"course": gin.H{
				"id":    course.Id,
				"title": course.Title,
			},
		}

		// Get assignments without submissions for this thread
		assignments, err := h.AssignmentClient.ListAssignmentsWithoutSubmissionForThread(
			c.Request.Context(),
			thread.Id,
			studentID,
		)
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error getting pending assignments for thread: %v", err),
				"all_pending_assignments",
				map[string]any{
					"student_id": studentID,
					"thread_id":  thread.Id,
					"error":      err.Error(),
				},
			)
			// Continue with other threads even if this one fails
			continue
		}

		// Format and add assignments to the combined list
		for _, item := range assignments {
			assignment := item.GetAssignment()

			// Calculate deadline status
			now := time.Now()
			dueDate := assignment.GetDueDate().AsTime()
			daysRemaining := int(dueDate.Sub(now).Hours() / 24)
			isOverdue := now.After(dueDate)

			var statusText string
			if isOverdue {
				statusText = "Overdue"
			} else if daysRemaining == 0 {
				statusText = "Due today"
			} else if daysRemaining == 1 {
				statusText = "Due tomorrow"
			} else {
				statusText = "Due in " + strconv.Itoa(daysRemaining) + " days"
			}

			allPendingAssignments = append(allPendingAssignments, gin.H{
				"id":                  assignment.GetId(),
				"week_id":             assignment.GetWeekId(),
				"title":               assignment.GetTitle(),
				"description":         assignment.GetDescription(),
				"due_date":            assignment.GetDueDate().AsTime().Format(time.RFC3339),
				"max_points":          assignment.GetMaxPoints(),
				"assignment_group_id": assignment.GetAssignmentGroupId(),
				"type":                assignment.GetType(),
				"created_at":          assignment.GetCreatedAt().AsTime().Format(time.RFC3339),
				"updated_at":          assignment.GetUpdatedAt().AsTime().Format(time.RFC3339),
				"thread":              threadInfo[thread.Id],
				"deadline_status": gin.H{
					"is_overdue":     isOverdue,
					"days_remaining": daysRemaining,
					"status_text":    statusText,
				},
			})
		}
	}

	// Sort assignments by due date (closest deadline first)
	sort.Slice(allPendingAssignments, func(i, j int) bool {
		dueDate1, _ := time.Parse(time.RFC3339, allPendingAssignments[i]["due_date"].(string))
		dueDate2, _ := time.Parse(time.RFC3339, allPendingAssignments[j]["due_date"].(string))
		return dueDate1.Before(dueDate2)
	})

	c.JSON(http.StatusOK, gin.H{
		"student_id":  studentID,
		"count":       len(allPendingAssignments),
		"assignments": allPendingAssignments,
	})
}

// GetStudentProfileHandler возвращает полную информацию о студенте включая degree программу
func (h *StudentHandler) GetStudentProfileHandler(c *gin.Context) {
	// Parse student ID
	studentIDStr := c.Param("user_id")
	studentID, err := strconv.ParseInt(studentIDStr, 10, 64)
	if err != nil || studentID <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid student ID"})
		return
	}

	// 1. Get basic user information
	user, err := h.UserClient.GetUserWithContext(c.Request.Context(), studentID)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error getting user info: %v", err),
			"student_profile",
			map[string]any{"student_id": studentID, "error": err.Error()},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to get user information"})
		return
	}

	// 2. Get student degree information
	studentDegreesResp, err := h.TranscriptClient.GetClient().GetStudentDegrees(
		c.Request.Context(),
		&transcriptpb.GetStudentDegreesRequest{UserId: studentID},
	)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error getting student degrees: %v", err),
			"student_profile",
			map[string]any{"student_id": studentID, "error": err.Error()},
		)
		// Continue without degree information
	}

	// 3. Get available courses for the student
	coursesResp, err := h.CourseClient.GetClient().GetCoursesForStudent(
		c.Request.Context(),
		&coursepb.GetCoursesForStudentRequest{UserId: studentID},
	)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error getting available courses: %v", err),
			"student_profile",
			map[string]any{"student_id": studentID, "error": err.Error()},
		)
		// Continue without courses information
	}

	// 4. Get enrolled threads
	threadsResp, err := h.ThreadClient.ListThreadsForUser(
		c.Request.Context(),
		&threadpb.UserThreadsRequest{UserId: studentID},
	)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error getting enrolled threads: %v", err),
			"student_profile",
			map[string]any{"student_id": studentID, "error": err.Error()},
		)
		// Continue without threads information
	}

	// 5. Calculate academic statistics
	var academicStats gin.H
	if studentDegreesResp != nil && len(studentDegreesResp.StudentDegrees) > 0 {
		// Get GPA and credits for the first degree (assuming one active degree)
		firstDegree := studentDegreesResp.StudentDegrees[0]

		gpaResp, err := h.TranscriptClient.GetClient().CalculateGPA(
			c.Request.Context(),
			&transcriptpb.CalculateGPARequest{UserId: studentID},
		)
		if err == nil {
			academicStats = gin.H{
				"gpa":                     gpaResp.Gpa,
				"total_credits_attempted": gpaResp.TotalCreditsAttempted,
				"total_credits_earned":    gpaResp.TotalCreditsEarned,
			}
		}

		// Add degree progress information
		if firstDegree.Degree != nil && gpaResp != nil {
			creditsProgress := float64(gpaResp.TotalCreditsEarned) / float64(firstDegree.Degree.RequiredCredits) * 100
			academicStats["degree_progress_percent"] = creditsProgress
			academicStats["required_credits"] = firstDegree.Degree.RequiredCredits
		}
	}

	// 6. Format degree information
	var degrees []gin.H
	if studentDegreesResp != nil {
		for _, studentDegree := range studentDegreesResp.StudentDegrees {
			degreeInfo := gin.H{
				"id":         studentDegree.Id,
				"degree_id":  studentDegree.DegreeId,
				"status":     studentDegree.Status.String(),
				"start_date": studentDegree.StartDate.AsTime().Format("2006-01-02"),
				"final_gpa":  studentDegree.FinalGpa,
			}

			if studentDegree.ExpectedGraduationDate != nil {
				degreeInfo["expected_graduation_date"] = studentDegree.ExpectedGraduationDate.AsTime().Format("2006-01-02")
			}

			if studentDegree.ActualGraduationDate != nil {
				degreeInfo["actual_graduation_date"] = studentDegree.ActualGraduationDate.AsTime().Format("2006-01-02")
			}

			if studentDegree.Degree != nil {
				degreeInfo["degree"] = gin.H{
					"id":               studentDegree.Degree.Id,
					"name":             studentDegree.Degree.Name,
					"level":            studentDegree.Degree.Level.String(),
					"description":      studentDegree.Degree.Description,
					"required_credits": studentDegree.Degree.RequiredCredits,
					"min_gpa":          studentDegree.Degree.MinGpa,
				}
			}

			degrees = append(degrees, degreeInfo)
		}
	}

	// 7. Format available courses
	var availableCourses []gin.H
	if coursesResp != nil {
		for _, course := range coursesResp.Courses {
			availableCourses = append(availableCourses, gin.H{
				"id":          course.Id,
				"title":       course.Title,
				"description": course.Description,
				"created_at":  course.CreatedAt.AsTime().Format(time.RFC3339),
				"updated_at":  course.UpdatedAt.AsTime().Format(time.RFC3339),
			})
		}
	}

	// 8. Format enrolled threads
	var enrolledThreads []gin.H
	if threadsResp != nil {
		for _, threadWithDetails := range threadsResp.Threads {
			thread := threadWithDetails.Thread
			course := threadWithDetails.Course

			enrolledThreads = append(enrolledThreads, gin.H{
				"id":           thread.Id,
				"title":        thread.Title,
				"max_students": thread.MaxStudents,
				"course": gin.H{
					"id":          course.Id,
					"title":       course.Title,
					"description": course.Description,
				},
			})
		}
	}

	// 9. Build comprehensive response
	response := gin.H{
		"student_id": studentID,
		"user": gin.H{
			"id":         user.Id,
			"name":       user.Name,
			"surname":    user.Surname,
			"email":      user.Email,
			"role":       user.Role,
			"created_at": user.CreatedAt,
			"updated_at": user.UpdatedAt,
		},
		"degrees":           degrees,
		"academic_stats":    academicStats,
		"available_courses": availableCourses,
		"enrolled_threads":  enrolledThreads,
		"summary": gin.H{
			"total_degrees":           len(degrees),
			"total_available_courses": len(availableCourses),
			"total_enrolled_threads":  len(enrolledThreads),
		},
	}

	c.JSON(http.StatusOK, response)
}
