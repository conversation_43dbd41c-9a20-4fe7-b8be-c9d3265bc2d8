package clients

import (
	"context"
	"fmt"

	analyticspb "github.com/olzzhas/edunite-server/analytics_service/pb/analytics"
	"google.golang.org/grpc"
)

// AnalyticsClient wraps the gRPC analytics service client
type AnalyticsClient struct {
	client analyticspb.AnalyticsServiceClient
}

// NewAnalyticsClient creates a new analytics client
func NewAnalyticsClient(conn *grpc.ClientConn) *AnalyticsClient {
	return &AnalyticsClient{
		client: analyticspb.NewAnalyticsServiceClient(conn),
	}
}

// GetOverview gets overview statistics
func (c *AnalyticsClient) GetOverview(ctx context.Context, period string) (*analyticspb.OverviewResponse, error) {
	req := &analyticspb.OverviewRequest{
		Period: period,
	}

	resp, err := c.client.GetOverview(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to get overview: %w", err)
	}

	return resp, nil
}

// GetUsersTimeline gets users timeline data
func (c *AnalyticsClient) GetUsersTimeline(ctx context.Context, period, granularity string) (*analyticspb.UsersTimelineResponse, error) {
	req := &analyticspb.UsersTimelineRequest{
		Period:      period,
		Granularity: granularity,
	}

	resp, err := c.client.GetUsersTimeline(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to get users timeline: %w", err)
	}

	return resp, nil
}

// GetUserDemographics gets user demographics data
func (c *AnalyticsClient) GetUserDemographics(ctx context.Context) (*analyticspb.UserDemographicsResponse, error) {
	req := &analyticspb.UserDemographicsRequest{}

	resp, err := c.client.GetUserDemographics(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to get user demographics: %w", err)
	}

	return resp, nil
}

// GetRecentActivities gets recent activities data
func (c *AnalyticsClient) GetRecentActivities(ctx context.Context, limit int32, activityType string) (*analyticspb.RecentActivitiesResponse, error) {
	req := &analyticspb.RecentActivitiesRequest{
		Limit: limit,
		Type:  activityType,
	}

	resp, err := c.client.GetRecentActivities(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to get recent activities: %w", err)
	}

	return resp, nil
}

// GetDetailedStats gets detailed statistics
func (c *AnalyticsClient) GetDetailedStats(ctx context.Context, period string, metrics []string) (*analyticspb.DetailedStatsResponse, error) {
	req := &analyticspb.DetailedStatsRequest{
		Period:  period,
		Metrics: metrics,
	}

	resp, err := c.client.GetDetailedStats(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to get detailed stats: %w", err)
	}

	return resp, nil
}

// GetPerformanceMetrics gets performance metrics
func (c *AnalyticsClient) GetPerformanceMetrics(ctx context.Context) (*analyticspb.PerformanceMetricsResponse, error) {
	req := &analyticspb.PerformanceMetricsRequest{}

	resp, err := c.client.GetPerformanceMetrics(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to get performance metrics: %w", err)
	}

	return resp, nil
}
