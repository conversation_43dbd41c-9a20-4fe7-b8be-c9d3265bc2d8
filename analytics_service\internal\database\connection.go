package database

import (
	"context"
	"log"
	"time"

	"github.com/jackc/pgx/v4/pgxpool"
	"github.com/olzzhas/edunite-server/analytics_service/internal/config"
)

// ConnectDB creates a connection pool to PostgreSQL database
func ConnectDB(cfg *config.Config) *pgxpool.Pool {
	var pool *pgxpool.Pool
	var err error

	// Retry connection up to 5 times
	for i := 0; i < 5; i++ {
		pool, err = pgxpool.Connect(context.Background(), cfg.Database.URL)
		if err == nil {
			log.Println("Connected to database successfully.")
			break
		}

		log.Printf("Failed to connect to database: %v. Retrying in 2 seconds...\n", err)
		time.Sleep(2 * time.Second)
	}

	if err != nil {
		log.Fatalf("Unable to connect to database after retries: %v\n", err)
	}

	// Test the connection
	if err := pool.Ping(context.Background()); err != nil {
		log.Fatalf("Unable to ping database: %v", err)
	}

	log.Println("Successfully connected to PostgreSQL database")
	return pool
}
