package database

import (
	"context"
	"log"

	"github.com/jackc/pgx/v4/pgxpool"
	"github.com/olzzhas/edunite-server/notification_service/internal/config"
)

// ConnectDB creates a connection pool to PostgreSQL database
func ConnectDB(cfg *config.Config) *pgxpool.Pool {
	dbpool, err := pgxpool.Connect(context.Background(), cfg.Database.URL)
	if err != nil {
		log.Fatalf("Unable to connect to database: %v", err)
	}

	// Test the connection
	if err := dbpool.Ping(context.Background()); err != nil {
		log.Fatalf("Unable to ping database: %v", err)
	}

	log.Println("Successfully connected to PostgreSQL database")
	return dbpool
}
