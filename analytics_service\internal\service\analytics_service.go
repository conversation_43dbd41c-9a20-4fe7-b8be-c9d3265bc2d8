package service

import (
	"context"
	"fmt"

	"github.com/olzzhas/edunite-server/analytics_service/internal/repository"
	analyticspb "github.com/olzzhas/edunite-server/analytics_service/pb/analytics"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type AnalyticsService struct {
	analyticspb.UnimplementedAnalyticsServiceServer
	repo *repository.AnalyticsRepository
}

func NewAnalyticsService(repo *repository.AnalyticsRepository) *AnalyticsService {
	return &AnalyticsService{
		repo: repo,
	}
}

func (s *AnalyticsService) GetOverview(ctx context.Context, req *analyticspb.OverviewRequest) (*analyticspb.OverviewResponse, error) {
	period := req.Period
	if period == "" {
		period = "last_30_days"
	}

	// Get current stats
	currentStats, err := s.repo.GetOverviewStats(ctx, period)
	if err != nil {
		return nil, fmt.Errorf("failed to get current stats: %w", err)
	}

	// Get previous period stats for comparison
	previousStats, err := s.repo.GetPreviousPeriodStats(ctx, period)
	if err != nil {
		return nil, fmt.Errorf("failed to get previous stats: %w", err)
	}

	// Calculate trends
	totalUsersStatValue := calculateStatValue(currentStats.TotalUsers, previousStats.TotalUsers)
	totalCoursesStatValue := calculateStatValue(currentStats.TotalCourses, previousStats.TotalCourses)
	totalThreadsStatValue := calculateStatValue(currentStats.TotalThreads, previousStats.TotalThreads)
	activeStudentsStatValue := calculateStatValue(currentStats.ActiveStudents, previousStats.ActiveStudents)

	return &analyticspb.OverviewResponse{
		TotalUsers:     totalUsersStatValue,
		TotalCourses:   totalCoursesStatValue,
		TotalThreads:   totalThreadsStatValue,
		ActiveStudents: activeStudentsStatValue,
		Period:         period,
	}, nil
}

func (s *AnalyticsService) GetUsersTimeline(ctx context.Context, req *analyticspb.UsersTimelineRequest) (*analyticspb.UsersTimelineResponse, error) {
	period := req.Period
	if period == "" {
		period = "30d"
	}

	granularity := req.Granularity
	if granularity == "" {
		granularity = "day"
	}

	timeline, err := s.repo.GetUsersTimeline(ctx, period, granularity)
	if err != nil {
		return nil, fmt.Errorf("failed to get users timeline: %w", err)
	}

	// Convert to protobuf format
	var timelinePoints []*analyticspb.TimelinePoint
	for _, point := range timeline {
		timelinePoints = append(timelinePoints, &analyticspb.TimelinePoint{
			Date:        point.Date,
			TotalUsers:  point.TotalUsers,
			NewUsers:    point.NewUsers,
			ActiveUsers: point.ActiveUsers,
		})
	}

	// For now, return empty comparison data
	// In a real implementation, you would calculate the previous period timeline
	var comparisonPoints []*analyticspb.TimelinePoint

	return &analyticspb.UsersTimelineResponse{
		Timeline:   timelinePoints,
		Comparison: comparisonPoints,
	}, nil
}

func (s *AnalyticsService) GetUserDemographics(ctx context.Context, req *analyticspb.UserDemographicsRequest) (*analyticspb.UserDemographicsResponse, error) {
	devices, locations, err := s.repo.GetUserDemographics(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get user demographics: %w", err)
	}

	// Convert devices to protobuf format
	var deviceItems []*analyticspb.DemographicItem
	for _, device := range devices {
		deviceItems = append(deviceItems, &analyticspb.DemographicItem{
			Name:       device.Name,
			Value:      device.Value,
			Percentage: device.Percentage,
		})
	}

	// Convert locations to protobuf format
	var locationItems []*analyticspb.DemographicItem
	for _, location := range locations {
		locationItems = append(locationItems, &analyticspb.DemographicItem{
			Name:       location.Name,
			Value:      location.Value,
			Percentage: location.Percentage,
		})
	}

	return &analyticspb.UserDemographicsResponse{
		Devices:   deviceItems,
		Locations: locationItems,
	}, nil
}

func (s *AnalyticsService) GetRecentActivities(ctx context.Context, req *analyticspb.RecentActivitiesRequest) (*analyticspb.RecentActivitiesResponse, error) {
	limit := req.Limit
	if limit == 0 {
		limit = 10
	}

	activityType := req.Type
	if activityType == "" {
		activityType = "all"
	}

	activities, err := s.repo.GetRecentActivities(ctx, limit, activityType)
	if err != nil {
		return nil, fmt.Errorf("failed to get recent activities: %w", err)
	}

	// Convert to protobuf format
	var activityItems []*analyticspb.Activity
	for _, activity := range activities {
		activityItem := &analyticspb.Activity{
			Id:   activity.ID,
			Type: activity.Type,
			User: &analyticspb.ActivityUser{
				Id:    activity.UserID,
				Name:  activity.Name,
				Email: activity.Email,
			},
			Date:   timestamppb.New(activity.Date),
			Status: activity.Status,
		}

		// Add course info if available
		if activity.CourseID != nil && activity.CourseTitle != nil {
			activityItem.Course = &analyticspb.ActivityCourse{
				Id:    *activity.CourseID,
				Title: *activity.CourseTitle,
			}
		}

		// Add assignment info if available
		if activity.AssignmentID != nil && activity.AssignmentTitle != nil {
			activityItem.Assignment = &analyticspb.ActivityAssignment{
				Id:    *activity.AssignmentID,
				Title: *activity.AssignmentTitle,
			}
		}

		activityItems = append(activityItems, activityItem)
	}

	return &analyticspb.RecentActivitiesResponse{
		Activities: activityItems,
	}, nil
}

// Helper function to calculate stat values with trends
func calculateStatValue(current, previous int64) *analyticspb.StatValue {
	var trend string
	var trendValue string

	if previous == 0 {
		trend = "neutral"
		trendValue = "0%"
	} else {
		change := float64(current-previous) / float64(previous) * 100
		if change > 0 {
			trend = "up"
			trendValue = fmt.Sprintf("+%.2f%%", change)
		} else if change < 0 {
			trend = "down"
			trendValue = fmt.Sprintf("%.2f%%", change)
		} else {
			trend = "neutral"
			trendValue = "0%"
		}
	}

	return &analyticspb.StatValue{
		Value:               current,
		Trend:               trend,
		TrendValue:          trendValue,
		PreviousPeriodValue: previous,
	}
}

func (s *AnalyticsService) GetDetailedStats(ctx context.Context, req *analyticspb.DetailedStatsRequest) (*analyticspb.DetailedStatsResponse, error) {
	period := req.Period
	if period == "" {
		period = "30d"
	}

	// Get course enrollments
	enrollments, err := s.repo.GetCourseEnrollments(ctx, period)
	if err != nil {
		return nil, fmt.Errorf("failed to get course enrollments: %w", err)
	}

	// Convert to protobuf format
	var enrollmentPoints []*analyticspb.CourseEnrollmentPoint
	for _, point := range enrollments {
		enrollmentPoints = append(enrollmentPoints, &analyticspb.CourseEnrollmentPoint{
			Date:        point.Date,
			Enrollments: point.Enrollments,
			Completions: point.Completions,
		})
	}

	// Get user activity by day
	userActivity, err := s.repo.GetUserActivityByDay(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get user activity: %w", err)
	}

	// Convert to protobuf format
	var activityDays []*analyticspb.UserActivityDay
	for _, day := range userActivity {
		activityDays = append(activityDays, &analyticspb.UserActivityDay{
			Day:         day.Day,
			ActiveUsers: day.ActiveUsers,
		})
	}

	// Get traffic sources
	trafficSources, err := s.repo.GetTrafficSources(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get traffic sources: %w", err)
	}

	// Convert to protobuf format
	var sources []*analyticspb.TrafficSource
	for _, source := range trafficSources {
		sources = append(sources, &analyticspb.TrafficSource{
			Source:     source.Source,
			Value:      source.Value,
			Percentage: source.Percentage,
		})
	}

	return &analyticspb.DetailedStatsResponse{
		CourseEnrollments: &analyticspb.CourseEnrollments{
			Timeline:   enrollmentPoints,
			Comparison: []*analyticspb.CourseEnrollmentPoint{}, // Empty for now
		},
		UserActivity: &analyticspb.UserActivity{
			Daily: activityDays,
		},
		TrafficSources: sources,
	}, nil
}

func (s *AnalyticsService) GetPerformanceMetrics(ctx context.Context, req *analyticspb.PerformanceMetricsRequest) (*analyticspb.PerformanceMetricsResponse, error) {
	metrics, err := s.repo.GetPerformanceMetrics(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get performance metrics: %w", err)
	}

	// Calculate trends (mock data for now)
	return &analyticspb.PerformanceMetricsResponse{
		CourseCompletionRate: &analyticspb.PerformanceMetric{
			Value:       metrics.CourseCompletionRate,
			Unit:        "%",
			Trend:       "up",
			Change:      "+0.5%",
			Description: "Course completion rate",
		},
		AverageGrade: &analyticspb.PerformanceMetric{
			Value:       metrics.AverageGrade,
			Unit:        "points",
			Trend:       "up",
			Change:      "+3.50",
			Description: "Average assignment grade",
		},
		AttendanceRate: &analyticspb.PerformanceMetric{
			Value:       metrics.AttendanceRate,
			Unit:        "%",
			Trend:       "down",
			Change:      "-2.1%",
			Description: "Average attendance rate",
		},
		AverageSessionDuration: &analyticspb.PerformanceMetric{
			Value:       0, // Not a numeric value
			Unit:        "time",
			Trend:       "up",
			Change:      "+15s",
			Description: "Average session duration",
		},
	}, nil
}
