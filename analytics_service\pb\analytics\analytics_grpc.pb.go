// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.2
// source: proto/analytics.proto

package analyticspb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	AnalyticsService_GetOverview_FullMethodName           = "/analyticspb.AnalyticsService/GetOverview"
	AnalyticsService_GetUsersTimeline_FullMethodName      = "/analyticspb.AnalyticsService/GetUsersTimeline"
	AnalyticsService_GetUserDemographics_FullMethodName   = "/analyticspb.AnalyticsService/GetUserDemographics"
	AnalyticsService_GetRecentActivities_FullMethodName   = "/analyticspb.AnalyticsService/GetRecentActivities"
	AnalyticsService_GetDetailedStats_FullMethodName      = "/analyticspb.AnalyticsService/GetDetailedStats"
	AnalyticsService_GetPerformanceMetrics_FullMethodName = "/analyticspb.AnalyticsService/GetPerformanceMetrics"
)

// AnalyticsServiceClient is the client API for AnalyticsService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AnalyticsServiceClient interface {
	// Dashboard Overview
	GetOverview(ctx context.Context, in *OverviewRequest, opts ...grpc.CallOption) (*OverviewResponse, error)
	// Users Timeline
	GetUsersTimeline(ctx context.Context, in *UsersTimelineRequest, opts ...grpc.CallOption) (*UsersTimelineResponse, error)
	// User Demographics
	GetUserDemographics(ctx context.Context, in *UserDemographicsRequest, opts ...grpc.CallOption) (*UserDemographicsResponse, error)
	// Recent Activities
	GetRecentActivities(ctx context.Context, in *RecentActivitiesRequest, opts ...grpc.CallOption) (*RecentActivitiesResponse, error)
	// Detailed Stats
	GetDetailedStats(ctx context.Context, in *DetailedStatsRequest, opts ...grpc.CallOption) (*DetailedStatsResponse, error)
	// Performance Metrics
	GetPerformanceMetrics(ctx context.Context, in *PerformanceMetricsRequest, opts ...grpc.CallOption) (*PerformanceMetricsResponse, error)
}

type analyticsServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAnalyticsServiceClient(cc grpc.ClientConnInterface) AnalyticsServiceClient {
	return &analyticsServiceClient{cc}
}

func (c *analyticsServiceClient) GetOverview(ctx context.Context, in *OverviewRequest, opts ...grpc.CallOption) (*OverviewResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(OverviewResponse)
	err := c.cc.Invoke(ctx, AnalyticsService_GetOverview_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticsServiceClient) GetUsersTimeline(ctx context.Context, in *UsersTimelineRequest, opts ...grpc.CallOption) (*UsersTimelineResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UsersTimelineResponse)
	err := c.cc.Invoke(ctx, AnalyticsService_GetUsersTimeline_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticsServiceClient) GetUserDemographics(ctx context.Context, in *UserDemographicsRequest, opts ...grpc.CallOption) (*UserDemographicsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UserDemographicsResponse)
	err := c.cc.Invoke(ctx, AnalyticsService_GetUserDemographics_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticsServiceClient) GetRecentActivities(ctx context.Context, in *RecentActivitiesRequest, opts ...grpc.CallOption) (*RecentActivitiesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RecentActivitiesResponse)
	err := c.cc.Invoke(ctx, AnalyticsService_GetRecentActivities_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticsServiceClient) GetDetailedStats(ctx context.Context, in *DetailedStatsRequest, opts ...grpc.CallOption) (*DetailedStatsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DetailedStatsResponse)
	err := c.cc.Invoke(ctx, AnalyticsService_GetDetailedStats_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *analyticsServiceClient) GetPerformanceMetrics(ctx context.Context, in *PerformanceMetricsRequest, opts ...grpc.CallOption) (*PerformanceMetricsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PerformanceMetricsResponse)
	err := c.cc.Invoke(ctx, AnalyticsService_GetPerformanceMetrics_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AnalyticsServiceServer is the server API for AnalyticsService service.
// All implementations must embed UnimplementedAnalyticsServiceServer
// for forward compatibility.
type AnalyticsServiceServer interface {
	// Dashboard Overview
	GetOverview(context.Context, *OverviewRequest) (*OverviewResponse, error)
	// Users Timeline
	GetUsersTimeline(context.Context, *UsersTimelineRequest) (*UsersTimelineResponse, error)
	// User Demographics
	GetUserDemographics(context.Context, *UserDemographicsRequest) (*UserDemographicsResponse, error)
	// Recent Activities
	GetRecentActivities(context.Context, *RecentActivitiesRequest) (*RecentActivitiesResponse, error)
	// Detailed Stats
	GetDetailedStats(context.Context, *DetailedStatsRequest) (*DetailedStatsResponse, error)
	// Performance Metrics
	GetPerformanceMetrics(context.Context, *PerformanceMetricsRequest) (*PerformanceMetricsResponse, error)
	mustEmbedUnimplementedAnalyticsServiceServer()
}

// UnimplementedAnalyticsServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAnalyticsServiceServer struct{}

func (UnimplementedAnalyticsServiceServer) GetOverview(context.Context, *OverviewRequest) (*OverviewResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOverview not implemented")
}
func (UnimplementedAnalyticsServiceServer) GetUsersTimeline(context.Context, *UsersTimelineRequest) (*UsersTimelineResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUsersTimeline not implemented")
}
func (UnimplementedAnalyticsServiceServer) GetUserDemographics(context.Context, *UserDemographicsRequest) (*UserDemographicsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserDemographics not implemented")
}
func (UnimplementedAnalyticsServiceServer) GetRecentActivities(context.Context, *RecentActivitiesRequest) (*RecentActivitiesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecentActivities not implemented")
}
func (UnimplementedAnalyticsServiceServer) GetDetailedStats(context.Context, *DetailedStatsRequest) (*DetailedStatsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDetailedStats not implemented")
}
func (UnimplementedAnalyticsServiceServer) GetPerformanceMetrics(context.Context, *PerformanceMetricsRequest) (*PerformanceMetricsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPerformanceMetrics not implemented")
}
func (UnimplementedAnalyticsServiceServer) mustEmbedUnimplementedAnalyticsServiceServer() {}
func (UnimplementedAnalyticsServiceServer) testEmbeddedByValue()                          {}

// UnsafeAnalyticsServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AnalyticsServiceServer will
// result in compilation errors.
type UnsafeAnalyticsServiceServer interface {
	mustEmbedUnimplementedAnalyticsServiceServer()
}

func RegisterAnalyticsServiceServer(s grpc.ServiceRegistrar, srv AnalyticsServiceServer) {
	// If the following call pancis, it indicates UnimplementedAnalyticsServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&AnalyticsService_ServiceDesc, srv)
}

func _AnalyticsService_GetOverview_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OverviewRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsServiceServer).GetOverview(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AnalyticsService_GetOverview_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsServiceServer).GetOverview(ctx, req.(*OverviewRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnalyticsService_GetUsersTimeline_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UsersTimelineRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsServiceServer).GetUsersTimeline(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AnalyticsService_GetUsersTimeline_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsServiceServer).GetUsersTimeline(ctx, req.(*UsersTimelineRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnalyticsService_GetUserDemographics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserDemographicsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsServiceServer).GetUserDemographics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AnalyticsService_GetUserDemographics_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsServiceServer).GetUserDemographics(ctx, req.(*UserDemographicsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnalyticsService_GetRecentActivities_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecentActivitiesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsServiceServer).GetRecentActivities(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AnalyticsService_GetRecentActivities_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsServiceServer).GetRecentActivities(ctx, req.(*RecentActivitiesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnalyticsService_GetDetailedStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DetailedStatsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsServiceServer).GetDetailedStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AnalyticsService_GetDetailedStats_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsServiceServer).GetDetailedStats(ctx, req.(*DetailedStatsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AnalyticsService_GetPerformanceMetrics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PerformanceMetricsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnalyticsServiceServer).GetPerformanceMetrics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AnalyticsService_GetPerformanceMetrics_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnalyticsServiceServer).GetPerformanceMetrics(ctx, req.(*PerformanceMetricsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AnalyticsService_ServiceDesc is the grpc.ServiceDesc for AnalyticsService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AnalyticsService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "analyticspb.AnalyticsService",
	HandlerType: (*AnalyticsServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetOverview",
			Handler:    _AnalyticsService_GetOverview_Handler,
		},
		{
			MethodName: "GetUsersTimeline",
			Handler:    _AnalyticsService_GetUsersTimeline_Handler,
		},
		{
			MethodName: "GetUserDemographics",
			Handler:    _AnalyticsService_GetUserDemographics_Handler,
		},
		{
			MethodName: "GetRecentActivities",
			Handler:    _AnalyticsService_GetRecentActivities_Handler,
		},
		{
			MethodName: "GetDetailedStats",
			Handler:    _AnalyticsService_GetDetailedStats_Handler,
		},
		{
			MethodName: "GetPerformanceMetrics",
			Handler:    _AnalyticsService_GetPerformanceMetrics_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/analytics.proto",
}
